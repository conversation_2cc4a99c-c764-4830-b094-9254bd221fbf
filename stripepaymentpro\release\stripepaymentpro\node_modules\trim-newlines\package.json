{"name": "trim-newlines", "version": "4.1.1", "description": "Trim newlines from the start and/or end of a string", "license": "MIT", "repository": "sindresorhus/trim-newlines", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["trim", "newline", "newlines", "linebreak", "lf", "crlf", "left", "right", "start", "end", "string", "remove", "delete", "strip"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}