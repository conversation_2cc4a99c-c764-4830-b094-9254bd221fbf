# appveyor file
# http://www.appveyor.com/docs/appveyor-yml

# branches to build
branches:
  # whitelist
  only:
    - master

# build version format
version: "{build}"

# what combinations to test
environment:
  matrix:
    - nodejs_version: 5
    - nodejs_version: 4
    - nodejs_version: 0.12

# Get the latest stable version of Node 0.STABLE.latest
install:
  - ps: Update-NodeJsInstallation (Get-NodeJsLatestBuild $env:nodejs_version)
  - npm install

build: off

test_script:
  - node --version
  - npm --version
  - cmd: npm test
