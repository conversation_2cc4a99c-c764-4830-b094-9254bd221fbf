<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

/**
 * Controller for webhook settings and payment success handling
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_stripepaymentpro_webhook_controller {

    /**
     * Stripe client for communication with stripe
     */
    private $stripeclient;

    /**
     * Instance of stripe payment pro plugin
     */
    private $plugin;

    /**
     * Instance of stripe payment plugin
     */
    private $plugincore;

    /**
     * constructor for class
     */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepaymentpro');
        try {
            $this->stripeclient  = new \Stripe\StripeClient($this->plugin->getcurrentsecretkey());
        } catch (\Exception $e){
            \core\notification::error($e->getMessage());
        }
    }

    /**
     * funtion for create webhook endpont and connect with our application
     */
    public function createwebhook() {
        global $CFG;
        $usertoken = get_config('enrol_stripepaymentpro', 'webservice_token');
        error_log("Using webhook token: " . $usertoken);
        try {
            $webhook = $this->stripeclient->webhookEndpoints->create([
                'enabled_events' => [
                    'checkout.session.completed',
                    'checkout.session.async_payment_failed',
                    'customer.subscription.deleted',
                    'customer.subscription.updated',
                ],
                'url' => $CFG->wwwroot . '/webservice/rest/server.php?wstoken='.$usertoken.'&wsfunction=moodle_stripepaymentpro_webhook_handler',
            ]);

            // Save the webhook ID for future use (deletion, verification, etc.)
            set_config('stripewebhookid', $webhook->id, 'enrol_stripepaymentpro');
            set_config('stripewebhooksecret', $webhook->secret, 'enrol_stripepaymentpro');
        } catch ( \Exception $e ) {
            \core\notification::error($e->getMessage());
        }
    }

    /**
     * function for delete webhook if the license is deactive
     *
     * @param $webhookid string id of the webhook connected to our application
     */
    public function deletewebhook($webhookid) {
        global $CFG;

        if (!$webhookid) {
            $error = 'No webhook ID found in configuration';
            \core\notification::error($error);
            return false;
        }

        try {
            $this->stripeclient->webhookEndpoints->delete($webhookid, []);
            // Set the webhook ID configuration to null
            set_config('stripewebhookid', null, 'enrol_stripepaymentpro');
            set_config('stripewebhooksecret', null, 'enrol_stripepaymentpro');
            return true;
        } catch (\Exception $e) {
            // If webhook can't be deleted (e.g., doesn't exist or API key mismatch), just clear config
            set_config('stripewebhookid', null, 'enrol_stripepaymentpro');
            set_config('stripewebhooksecret', null, 'enrol_stripepaymentpro');
            return false;
        }
    }

    /**
     * Remove existing webhook and create a new one with current API keys
     *
     * @return bool Success status
     */
    public function recreatewebhook() {
        global $CFG;

        // Get existing webhook ID
        $existingwebhookid = get_config('enrol_stripepaymentpro', 'stripewebhookid');

        // Try to delete existing webhook if it exists
        if ($existingwebhookid) {
            $this->deletewebhook($existingwebhookid);
        }

        // Create new webhook
        try {
            $this->createwebhook();
            return true;
        } catch (\Exception $e) {
            \core\notification::error('Failed to create new webhook: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle payment success redirect (migrated from thankyou.php)
     *
     * @param string $sessionid Stripe session ID
     * @return void
     */
    public function handlepaymentsuccess($sessionid) {
        global $DB, $USER;

        if (empty($sessionid)) {
            throw new \moodle_exception('invalidsessionid', 'enrol_stripepaymentpro');
        }

        try {
            $session = \Stripe\Checkout\Session::retrieve($sessionid);
        } catch (\Exception $e) {
            \core\notification::error($e->getMessage());
            throw new \moodle_exception('stripeerror', 'enrol_stripepaymentpro', '', $e->getMessage());
        }

        // Check if the payment status is 'paid'
        if ($session->payment_status === 'paid') {
            // Retrieve metadata
            $instanceid = $session->metadata->instanceid;
            $userid = $session->metadata->userid;

            // Get the course ID from the enrolment instance
            $enrolinstance = $DB->get_record('enrol', ['id' => $instanceid], '*', MUST_EXIST);
            $course = $DB->get_record('course', ['id' => $enrolinstance->courseid], '*', MUST_EXIST);

            // Check if user is already enrolled
            $context = \context_course::instance($course->id);
            if (!is_enrolled($context, $userid)) {
                // User is not enrolled yet, trigger webhook processing to ensure enrollment
                require_once(__DIR__ . '/../helper/webhook_helper.php');
                $webhookHelper = new \enrol_stripepaymentpro\helper\webhook_helper($sessionid);
                $webhookHelper->successfullenrolment();
            }

            // Redirect to the course view page
            redirect(new \moodle_url('/course/view.php', ['id' => $course->id]));
        } else {
            // If payment status is not 'paid', display an error message
            throw new \moodle_exception('paymentnotsuccessful', 'enrol_stripepaymentpro');
        }
    }
}
