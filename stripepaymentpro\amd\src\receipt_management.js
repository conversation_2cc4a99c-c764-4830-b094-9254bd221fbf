// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Receipt management functionality for stripepaymentpro plugin
 *
 * @module     enrol_stripepaymentpro/receipt_management
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import Str from 'core/str';

let localized = {};

export const init = () => {
    Str.get_strings([
        {key: 'copied', component: 'enrol_stripepaymentpro'},
        {key: 'failed', component: 'enrol_stripepaymentpro'},
        {key: 'nosearchresults', component: 'enrol_stripepaymentpro'}
    ]).then(strings => {
        localized = {
            copied: strings[0],
            failed: strings[1],
            noResults: strings[2]
        };

        initializeBulkActions();
        initializeSearch();
        initializeCopyFunctionality();
        initializeFilters();
    }).catch(error => {
        console.error('Failed to load localized strings:', error);
    });
};

const initializeBulkActions = () => {
    const selectAllCheckbox = document.getElementById('select-all-receipts');
    const bulkActionSelect = document.getElementById('bulk-action-select');
    const applyButton = document.getElementById('apply-bulk-action');

    if (!bulkActionSelect || !applyButton) return;

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const receiptCheckboxes = document.querySelectorAll('.receipt-checkbox');
            receiptCheckboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = this.checked;
                }
            });
        });
    }

    // Handle individual checkboxes - use event delegation since checkboxes might be added dynamically
    document.addEventListener('change', function(event) {
        if (event.target.classList.contains('receipt-checkbox')) {
            const receiptCheckboxes = document.querySelectorAll('.receipt-checkbox');
            const enabledCheckboxes = Array.from(receiptCheckboxes).filter(cb => !cb.disabled);
            const checkedBoxes = enabledCheckboxes.filter(cb => cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedBoxes.length === enabledCheckboxes.length && enabledCheckboxes.length > 0;
                selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < enabledCheckboxes.length;
            }
        }
    });

    // Handle bulk action apply
    applyButton.addEventListener('click', function() {
        const selectedAction = bulkActionSelect.value;
        const checkedBoxes = document.querySelectorAll('.receipt-checkbox:checked');
        
        if (selectedAction === 'download' && checkedBoxes.length > 0) {
            const receiptUrls = Array.from(checkedBoxes).map(checkbox => {
                return checkbox.closest('tr').dataset.receiptUrl;
            }).filter(url => url && url.trim() !== '');

            if (receiptUrls.length > 0) {
                // Open each receipt URL in a new tab/window
                receiptUrls.forEach((url, index) => {
                    setTimeout(() => {
                        window.open(url, '_blank');
                    }, index * 500); // Stagger the opening to avoid popup blockers
                });
                
                alert(`Opening ${receiptUrls.length} receipt(s) for download.`);
            } else {
                alert('No valid receipt URLs found for selected items.');
            }
        } else if (checkedBoxes.length === 0) {
            alert('Please select at least one receipt.');
        }
    });
};

const initializeSearch = () => {
    const searchInput = document.getElementById('receipt-search');
    const clearButton = document.getElementById('clear-search');
    const table = document.getElementById('receipts-table');
    
    if (!searchInput || !table) return;

    const performSearch = () => {
        const searchTerm = searchInput.value.toLowerCase();
        const courseFilter = document.getElementById('course-filter-select');
        const dateFilter = document.getElementById('date-filter-select');
        const selectedCourse = courseFilter ? courseFilter.value : 'All course';
        const selectedDateFilter = dateFilter ? dateFilter.value : 'all';
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;

        rows.forEach(function(row) {
            const courseName = row.getAttribute('datacoursename')?.toLowerCase() || '';
            const studentName = row.getAttribute('data-student-name')?.toLowerCase() || '';
            const studentEmail = row.getAttribute('data-student-email')?.toLowerCase() || '';
            const paymentDate = row.getAttribute('data-payment-date') || '';

            // Check search term match
            const searchMatches = !searchTerm || courseName.includes(searchTerm) ||
                                 studentName.includes(searchTerm) ||
                                 studentEmail.includes(searchTerm);

            // Check course filter match
            const courseMatches = selectedCourse === 'All course' ||
                                 row.getAttribute('datacoursename') === selectedCourse;

            // Check date filter match
            const dateMatches = checkDateFilter(paymentDate, selectedDateFilter);

            const shouldShow = searchMatches && courseMatches && dateMatches;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        updateNoResultsMessage(visibleCount, searchTerm);
    };

    const updateNoResultsMessage = (visibleCount, searchTerm) => {
        let noResultsRow = table.querySelector('.noresultsrow');

        if (visibleCount === 0 && searchTerm !== '') {
            if (!noResultsRow) {
                noResultsRow = document.createElement('tr');
                noResultsRow.className = 'noresultsrow';
                noResultsRow.innerHTML = `<td colspan="6" class="text-center text-muted py-4">
                    <i class="fa fa-search mr-2"></i>${localized.noResults}
                </td>`;
                table.querySelector('tbody').appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else if (noResultsRow) {
            noResultsRow.style.display = 'none';
        }
    };

    const clearSearch = () => {
        searchInput.value = '';
        performSearch();
        searchInput.focus();
    };

    searchInput.addEventListener('input', performSearch);
    searchInput.addEventListener('keyup', e => { if (e.key === 'Escape') clearSearch(); });
    if (clearButton) clearButton.addEventListener('click', clearSearch);
};

const initializeCopyFunctionality = () => {
    document.addEventListener('click', event => {
        const button = event.target.closest('.copy-receipt-url');
        if (!button) return;

        const receiptUrl = button.getAttribute('data-receipt-url');
        if (!receiptUrl) return;

        const originalText = button.innerHTML;
        
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(receiptUrl).then(() => {
                showCopySuccess(button, originalText);
            }).catch(() => {
                fallbackCopyTextToClipboard(receiptUrl, button, originalText);
            });
        } else {
            fallbackCopyTextToClipboard(receiptUrl, button, originalText);
        }
    });
};

const showCopySuccess = (button, originalText) => {
    button.innerHTML = `<i class="fa fa-check text-success"></i> ${localized.copied}`;
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
};

const fallbackCopyTextToClipboard = (text, button, originalText) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showCopySuccess(button, originalText);
    } catch (err) {
        button.innerHTML = `<i class="fa fa-times text-danger"></i> ${localized.failed}`;
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }
    
    document.body.removeChild(textArea);
};

const initializeFilters = () => {
    const courseFilter = document.getElementById('course-filter-select');
    const dateFilter = document.getElementById('date-filter-select');
    const table = document.getElementById('receipts-table');

    if (!courseFilter || !table) return;

    // Populate course filter with actual courses
    const courses = new Set();
    const rows = table.querySelectorAll('tbody tr.receipt-row');

    rows.forEach(row => {
        const courseName = row.getAttribute('datacoursename');
        if (courseName) {
            courses.add(courseName);
        }
    });

    // Clear existing options except the first one
    while (courseFilter.children.length > 1) {
        courseFilter.removeChild(courseFilter.lastChild);
    }

    // Add course options
    courses.forEach(courseName => {
        const option = document.createElement('option');
        option.value = courseName;
        option.textContent = courseName;
        courseFilter.appendChild(option);
    });

    // Handle filter changes
    const handleFilterChange = () => {
        const searchInput = document.getElementById('receipt-search');
        if (searchInput) {
            // Trigger search to update results with new filters
            searchInput.dispatchEvent(new Event('input'));
        }
    };

    courseFilter.addEventListener('change', handleFilterChange);
    if (dateFilter) {
        dateFilter.addEventListener('change', handleFilterChange);
    }
};

const checkDateFilter = (paymentDate, filterValue) => {
    if (filterValue === 'all' || !paymentDate) {
        return true;
    }

    const paymentDateTime = new Date(paymentDate);
    const now = new Date();

    switch (filterValue) {
        case 'today':
            return paymentDateTime.toDateString() === now.toDateString();

        case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            return paymentDateTime >= weekAgo;

        case 'month':
            const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
            return paymentDateTime >= monthAgo;

        case 'year':
            const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
            return paymentDateTime >= yearAgo;

        default:
            return true;
    }
};
