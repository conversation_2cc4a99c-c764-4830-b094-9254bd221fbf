    .strip-subcription-plan-details {
        max-width: 400px;
        width: 100%;
        margin: auto;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 0.25rem;
        margin-bottom: 0.75rem;
    }

    .strip-subcription-plan-details.details-content {
        flex-direction: column;
    }

    .strip-subcription-plan-details p {
        margin: 0;
        font-weight: 600;
    }

    button#apply {
        color: #fff;
        border: 0;
        padding: 5px 16px;
        border-radius: 0.5rem;
        font-size: 13px;
    }

    button#payButton,
    button#card-button-zero {
        color: #fff;
        border: 0;
        padding: 5px 32px;
        border-radius: 0.25rem;
        font-size: 13px;
        box-shadow: 0 0.125rem 0.25rem #645cff2e;
        width: 100%;
    }

    .displaynone {
        display: none;
    }

    .container:has(.table-striped) {
        margin: 2rem 0 0 0;
        max-width: 100%;
        padding: 0;
        overflow-x: auto;
    }

    /* My Subscriptions page styling */
    .subscription-management-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .subscription-management-container h2 {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .subscription-table-wrapper {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow-x: auto;
        overflow-y: visible;
        margin-bottom: 20px;
        width: 100%;
    }

    .subscription-table-wrapper .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        min-width: 800px;
        table-layout: auto;
    }

    .subscription-table-wrapper .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 12px 15px;
        text-align: left;
        white-space: nowrap;
    }

    .subscription-table-wrapper .table td {
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #dee2e6;
    }

    /* Ensure action column is always visible */
    .subscription-table-wrapper .table th:last-child,
    .subscription-table-wrapper .table td:last-child {
        min-width: 120px;
        width: 120px;
        text-align: center;
    }

    .subscription-table-wrapper .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .subscription-table-wrapper .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Status badges */
    .subscription-status {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: capitalize;
    }

    .subscription-status.active {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .subscription-status.canceled,
    .subscription-status.cancelled {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .subscription-status.past_due {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .subscription-status.incomplete {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    /* Date formatting */
    .subscription-date {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 0.9rem;
        color: #495057;
        white-space: nowrap;
    }

    /* Course name styling */
    .subscription-course-name {
        font-weight: 500;
        color: #007bff;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Action buttons */
    .subscription-action-btn {
        padding: 6px 12px;
        font-size: 0.875rem;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s ease;
    }

    .subscription-action-btn.btn-cancel {
        background-color: #dc3545;
        color: white;
        border: 1px solid #dc3545;
    }

    .subscription-action-btn.btn-cancel:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
        text-decoration: none;
    }

    .subscription-inactive-text {
        color: #6c757d;
        font-style: italic;
        font-size: 0.875rem;
    }

    /* No subscriptions message */
    .no-subscriptions-message {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
        font-size: 1.1rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .no-subscriptions-message i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #adb5bd;
    }

    /* Responsive design for My Subscriptions */
    @media (max-width: 768px) {
        .subscription-management-container {
            padding: 10px;
        }

        .subscription-table-wrapper {
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .subscription-table-wrapper .table {
            min-width: 700px;
        }

        .subscription-table-wrapper .table th,
        .subscription-table-wrapper .table td {
            padding: 8px 10px;
            font-size: 0.875rem;
            white-space: nowrap;
        }

        .subscription-course-name {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .subscription-date {
            font-size: 0.8rem;
        }

        .subscription-action-btn {
            padding: 4px 8px;
            font-size: 0.8rem;
        }
    }

        /* Stack table on very small screens */
        @media (max-width: 576px) {
            .subscription-table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .subscription-table-wrapper .table {
                min-width: 600px;
            }

            .subscription-management-container h2 {
                font-size: 1.25rem;
                text-align: center;
            }

            .no-subscriptions-message {
                padding: 30px 15px;
                font-size: 1rem;
            }

            .no-subscriptions-message i {
                font-size: 2.5rem;
            }
        }

    /* Print styles */
    @media print {
        .subscription-action-btn {
            display: none;
        }

        .subscription-table-wrapper {
            box-shadow: none;
            border: 1px solid #000;
        }

        .subscription-table-wrapper .table th {
            background-color: #f0f0f0 !important;
            -webkit-print-color-adjust: exact;
        }
    }

    .table-striped thead th {
        vertical-align: middle;
    }

    .table-striped .btnsecondary:not(:disabled):not(.disabled):active:focus {
        box-shadow: none;
        outline: none;
    }

    .table-striped .btnsecondary:focus,
    .btnsecondary.focus {
        outline: none;
        box-shadow: none;
    }

    .generate-coupon-section {
        display: none;
    }

    #page.drawers div[role="main"]:has(.strip-coupon-navigation-section) {
        padding: 0 !important;
    }

    .course-info {
        display: flex;
        justify-content: space-between;
    }

    .strip-coupon-navigation-section {
        display: flex;
        max-width: -moz-fit-content;
        max-width: fit-content;
        width: 100%;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        margin-bottom: 1rem;
    }

    .strip-coupon-navigation-section button {
        background-color: transparent;
        padding: 0.5rem;
        cursor: pointer;
        border: none;
        outline: none;
        box-shadow: none;
    }

    .strip-coupon-navigation-section button.active {
        color: #0f6cbf;
        border-bottom: 1px solid #0f6cbf;
    }

    .all-coupons-section {
        overflow: auto;
        width: 100%;
    }

    .all-coupons-section table {
        width: 100%;
    }

    .all-coupons-section table thead tr {
        padding: 0.25rem;
    }

    .all-coupons-section table thead tr th {
        padding: 0.5rem 1rem;
        border: 1px dotted #dee2e6;
    }

    .all-coupons-section table tbody tr {
        padding: 0.25rem;
    }

    .all-coupons-section table tbody tr td {
        padding: 0.5rem 1rem;
        border: 1px dotted #dee2e6;
        min-height: 53px;
    }

    .all-coupons-section table tbody tr td.coupon-list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .all-coupons-section table tbody tr td .coupon-name {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        background: #ececec;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .all-coupons-section table tbody tr td .coupon-name button {
        background: transparent;
        padding: 0;
        outline: none;
        border: none;
        box-shadow: none;
        font-size: 0.75rem;
        color: rgba(188, 0, 0, 0.8509803922);
    }
    .all-coupons-section table tbody tr td .deactivate-all-coupons {
        outline: none;
        border: none;
        box-shadow: none;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        background-color: #ececec;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        border-radius: 0.25rem;
    }

    .all-coupons-section table tbody tr td .deactivate-all-coupons i {
        background: transparent;
        padding: 0;
        outline: none;
        border: none;
        box-shadow: none;
        font-size: 0.75rem;
        color: rgba(188, 0, 0, 0.8509803922);
    }

/* Enhanced enrolment page styling */
.enrol-stripepaymentpro-enrol-page .pay-btn {
    margin-top: 18px;
    width: 100%;
    background-color: #0070f3;
    color: white;
    border: none;
    padding: 12px 0;
    font-size: 16px;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
}

.enrol-stripepaymentpro-enrol-page .pay-btn:hover {
    background-color: #005fd1;
    filter: brightness(0.9);
}

.enrol-stripepaymentpro-enrol-page .pay-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.enrol-stripepaymentpro-enrol-page .paydetailwrapper {
    padding: 2rem 1rem;
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e5e9;
}

.enrol-stripepaymentpro-enrol-page .paydetailheading,
.enrol-stripepaymentpro-enrol-page .heading {
    font-size: 1.25rem;
    margin: 0 0 1rem 0;
    color: #333;
    font-weight: 600;
    text-align: center;
}

.enrol-stripepaymentpro-enrol-page .paydetail .heading.trial-info {
    color: #28a745;
    font-weight: 600;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

.enrol-stripepaymentpro-enrol-page .price-row {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.enrol-stripepaymentpro-enrol-page .price-row .price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0070f3;
    line-height: 1;
}

.enrol-stripepaymentpro-enrol-page .price-row .duration {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.enrol-stripepaymentpro-enrol-page .price-row .duration span {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.enrol-stripepaymentpro-enrol-page ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.enrol-stripepaymentpro-enrol-page li.total {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.enrol-stripepaymentpro-enrol-page li.total:last-child {
    border-bottom: none;
}

.enrol-stripepaymentpro-enrol-page li.total .details {
    flex: 1;
}

.enrol-stripepaymentpro-enrol-page li.total .details .name {
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    margin: 0 0 0.25rem 0;
}

.enrol-stripepaymentpro-enrol-page li.total .details .duration {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
}

.enrol-stripepaymentpro-enrol-page li.total .price {
    text-align: right;
    margin-left: 1rem;
}

.enrol-stripepaymentpro-enrol-page li.total .price .total-price {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Total section styling */
.enrol-stripepaymentpro-enrol-page .total {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.875rem 0 !important;
    border-top: 2px solid #333 !important;
    margin-top: 0.875rem !important;
    margin-bottom: 0 !important;
}

.enrol-stripepaymentpro-enrol-page .total .details {
    flex: 1;
    margin: 0;
}

.enrol-stripepaymentpro-enrol-page .total .details .name {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

.enrol-stripepaymentpro-enrol-page .total .price {
    text-align: right !important;
    margin: 0;
}

.enrol-stripepaymentpro-enrol-page .total .price div {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

/* Discount section styling */
.enrol-stripepaymentpro-enrol-page .discount-section {
    border-top: 1px solid #eee;
    padding-top: 0.875rem;
    margin-bottom: 0.875rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.875rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row .details .name {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.enrol-stripepaymentpro-enrol-page .discount-row .details .discount-note {
    font-size: 0.875rem;
    color: #6f6f6f;
    margin-top: 0.25rem;
}

.enrol-stripepaymentpro-enrol-page .discount-row .price .discount-amount {
    font-size: 1rem;
    font-weight: 600;
    color: #00a86b;
}

.enrol-stripepaymentpro-enrol-page .promotion-code {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.enrol-stripepaymentpro-enrol-page .promotion-code .couponcode-text {
    display: block;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.enrol-stripepaymentpro-enrol-page .promotion-code .couponcode-wrap {
    width: 100%;
}

/* Error message styling */
.enrol-stripepaymentpro-enrol-page .error-message {
    margin: 1rem 0;
    padding: 0.75rem;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    font-size: 0.875rem;
    display: none;
}

.enrol-stripepaymentpro-enrol-page .error-message:not(:empty) {
    display: block;
}

.enrol-stripepaymentpro-enrol-page .error-message p {
    margin: 0;
    font-weight: 500;
}

.enrol-stripepaymentpro-enrol-page #card-button-zero {
    background-color: #28a745;
}

.enrol-stripepaymentpro-enrol-page #card-button-zero:hover {
    background-color: #218838;
}

/* Enhanced coupon input styling */
.enrol-stripepaymentpro-enrol-page .stripe-coupon-input-container {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 12px;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #fff;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input:focus {
    outline: none;
    border-color: #0070f3;
    box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-input.success {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 16px;
    font-size: 14px;
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
    min-width: 100px;
    transition: background-color 0.2s ease, transform 0.1s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply:hover:not(:disabled) {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
}

.enrol-stripepaymentpro-enrol-page .stripe-coupon-apply.loading {
    background-color: #0070f3;
}

.enrol-stripepaymentpro-enrol-page .apply-spinner {
    display: inline-block;
}

/* Enhanced message styling */
.enrol-stripepaymentpro-enrol-page .coupon-message-container {
    margin-top: 8px;
    min-height: 24px;
}

.enrol-stripepaymentpro-enrol-page .coupon-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    animation: slideIn 0.3s ease-out;
}

.enrol-stripepaymentpro-enrol-page .coupon-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.enrol-stripepaymentpro-enrol-page .coupon-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.enrol-stripepaymentpro-enrol-page .coupon-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.enrol-stripepaymentpro-enrol-page .hidden {
    display: none !important;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.enrol-stripepaymentpro-enrol-page .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 6px;
}

/* Accessibility and help text */
.enrol-stripepaymentpro-enrol-page .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.enrol-stripepaymentpro-enrol-page .coupon-help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* Focus management for better accessibility */
.enrol-stripepaymentpro-enrol-page .stripe-coupon-input:focus,
.enrol-stripepaymentpro-enrol-page #apply:focus {
    outline: 2px solid #0070f3;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .enrol-stripepaymentpro-enrol-page .coupon-error {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #dc3545;
    }

    .enrol-stripepaymentpro-enrol-page .coupon-success {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #28a745;
    }

    .enrol-stripepaymentpro-enrol-page .coupon-warning {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #ffc107;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .enrol-stripepaymentpro-enrol-page .coupon-message,
    .enrol-stripepaymentpro-enrol-page .spinner {
        animation: none;
    }

    .enrol-stripepaymentpro-enrol-page #apply {
        transition: none;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .enrol-stripepaymentpro-enrol-page .paydetailwrapper {
        margin: 1rem;
        padding: 1.5rem 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .enrol-stripepaymentpro-enrol-page .price-row {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .enrol-stripepaymentpro-enrol-page .price-row .price {
        font-size: 2rem;
    }

    .enrol-stripepaymentpro-enrol-page .price-row .duration span {
        font-size: 0.8rem;
    }

    .enrol-stripepaymentpro-enrol-page .heading {
        font-size: 1.125rem;
        text-align: center;
    }

    .enrol-stripepaymentpro-enrol-page li.total {
        padding: 0.5rem 0;
    }

    .enrol-stripepaymentpro-enrol-page li.total .details .name {
        font-size: 0.9rem;
    }

    .enrol-stripepaymentpro-enrol-page li.total .price .total-price {
        font-size: 0.9rem;
    }

    .enrol-stripepaymentpro-enrol-page .stripe-coupon-input-container {
        flex-direction: column;
        gap: 12px;
    }

    .enrol-stripepaymentpro-enrol-page .stripe-coupon-input,
    .enrol-stripepaymentpro-enrol-page .stripe-coupon-apply {
        width: 100%;
    }

    .enrol-stripepaymentpro-enrol-page .stripe-coupon-apply {
        min-width: auto;
        padding: 12px 16px;
    }

    .enrol-stripepaymentpro-enrol-page .pay-btn {
        padding: 14px 20px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .enrol-stripepaymentpro-enrol-page .paydetailwrapper {
        margin: 0.5rem;
        padding: 1rem 0.75rem;
    }

    .enrol-stripepaymentpro-enrol-page .price-row .price {
        font-size: 1.75rem;
    }

    .enrol-stripepaymentpro-enrol-page .heading {
        font-size: 1rem;
    }

    .enrol-stripepaymentpro-enrol-page li.total .details .name,
    .enrol-stripepaymentpro-enrol-page li.total .price .total-price {
        font-size: 0.875rem;
    }

    .enrol-stripepaymentpro-enrol-page .total .details .name,
    .enrol-stripepaymentpro-enrol-page .total .price div {
        font-size: 1rem !important;
    }
}

/* Utility classes */
.enrol-stripepaymentpro-hidden {
    display: none !important;
}

.enrol-stripepaymentpro-payment-element {
    width: 100%;
}

/* input style start */
.row input:focus,
.row select:focus,
.row textarea:focus,
tbody input 
{
    box-shadow: none !important;
}