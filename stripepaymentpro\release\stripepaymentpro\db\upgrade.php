<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON>od<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * database code for changing schema
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
function xmldb_enrol_stripepaymentpro_upgrade($oldversion) {
    global $DB;

    $dbman = $DB->get_manager();

    if ($oldversion < **********) {

        // Define table enrol_stripepro_coupons to be created.
        $table = new xmldb_table('enrol_stripepro_coupons');

        // Adding fields to table enrol_stripepro_coupons.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('couponid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('couponname', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('amount_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('percent_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('currency', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('duration', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('no_of_months', XMLDB_TYPE_INTEGER, '10', null, false);
        $table->add_field('stripe_product_id', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);
        $table->add_field('coupon_expiry', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);

        // Adding keys to table enrol_stripepro_coupons.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('unique_coupon_course', XMLDB_KEY_UNIQUE, ['couponid']);

        // Conditionally launch create table for enrol_stripepro_coupons.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Stripe savepoint reached.
        upgrade_plugin_savepoint(true, **********, 'enrol', 'stripepaymentpro');
    }

    if ($oldversion < 2025073007) {

        // Define table enrol_stripepaymentpro to be checked.
        $table = new xmldb_table('enrol_stripepaymentpro');

        // Remove legacy fields that are not used by Stripe payment processing.
        $field = new xmldb_field('business');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('option_name1');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('option_selection1_x');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('option_name2');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('option_selection2_x');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('parent_txn_id');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        $field = new xmldb_field('payment_type');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }

        // Add a new 'currency' field to the enrol_stripepaymentpro table.
        $field = new xmldb_field('currency', XMLDB_TYPE_CHAR, '10', null, false, false, null, 'memo');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        // Add a new 'stripeemail' field to the enrol_stripepaymentpro table.
        // Check for both old and new field names
        $oldfield = new xmldb_field('coupon_id');
        $newfield = new xmldb_field('stripeemail', XMLDB_TYPE_CHAR, '255', null, false, false, null, 'couponid');
        if ($dbman->field_exists($table, $oldfield)) {
            $dbman->add_field($table, $newfield, 'coupon_id');
        } else if (!$dbman->field_exists($table, $newfield)) {
            $dbman->add_field($table, $newfield);
        }

        // Add a new 'receipturl' field to the enrol_stripepaymentpro table.
        $field = new xmldb_field('receipturl', XMLDB_TYPE_CHAR, '500', null, false, false, null, 'trialperiodend');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        // Rename existing fields to remove underscores.
        $field = new xmldb_field('receiver_email', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'receiveremail');
        }

        $field = new xmldb_field('receiver_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'receiverid');
        }

        $field = new xmldb_field('item_name', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'itemname');
        }

        $field = new xmldb_field('coupon_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'couponid');
        }

        $field = new xmldb_field('payment_status', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'paymentstatus');
        }

        $field = new xmldb_field('pending_reason', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'pendingreason');
        }

        $field = new xmldb_field('txn_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'txnid');
        }

        $field = new xmldb_field('product_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'productid');
        }

        $field = new xmldb_field('product_name', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'productname');
        }

        $field = new xmldb_field('product_type', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'producttype');
        }

        $field = new xmldb_field('subscription_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'subscriptionid');
        }

        $field = new xmldb_field('renewal_interval', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'renewalinterval');
        }

        $field = new xmldb_field('renewal_intervalperiod', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'renewalintervalperiod');
        }

        // Define table enrol_stripepaymentpro_coupons to be checked.
        // It's possible that the old table name was `enrol_stripepayment_coupons`
        // so we need to check and rename it if it exists.
        $oldtablename = new xmldb_table('enrol_stripepayment_coupons');
        $newtablename = new xmldb_table('enrol_stripepaymentpro_coupons');

        if ($dbman->table_exists($oldtablename)) {
            $dbman->rename_table($oldtablename, $newtablename);
        }

        $table = new xmldb_table('enrol_stripepaymentpro_coupons');
        
        $field = new xmldb_field('coupon_name', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'couponname');
        }

        $field = new xmldb_field('amount_off', XMLDB_TYPE_FLOAT, '20,2', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'amountoff');
        }

        $field = new xmldb_field('percent_off', XMLDB_TYPE_FLOAT, '20,2', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'percentoff');
        }
        
        $field = new xmldb_field('no_of_months', XMLDB_TYPE_INTEGER, '10', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'noofmonths');
        }
        
        $field = new xmldb_field('stripe_product_id', XMLDB_TYPE_CHAR, '255', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'stripeproductid');
        }
        
        $field = new xmldb_field('coupon_expiry', XMLDB_TYPE_INTEGER, '10', null, false, false);
        if ($dbman->field_exists($table, $field)) {
            $dbman->rename_field($table, $field, 'couponexpiry');
        }
        
        // Drop the old key if it exists.
        try {
            $oldkey = new xmldb_key('unique_coupon_course', XMLDB_KEY_UNIQUE, ['couponid']);
            if ($dbman->find_key_name($table, $oldkey)) {
                $dbman->drop_key($table, $oldkey);
            }
        } catch (Exception $e) {
            // Key might not exist, continue
        }

        // Add the new key if it doesn't exist.
        try {
            $newkey = new xmldb_key('unique_couponid', XMLDB_KEY_UNIQUE, ['couponid']);
            if (!$dbman->find_key_name($table, $newkey)) {
                $dbman->add_key($table, $newkey);
            }
        } catch (Exception $e) {
            // Key might already exist, continue
        }

        // Stripe savepoint reached.
        upgrade_plugin_savepoint(true, 2025073007, 'enrol', 'stripepaymentpro');
    }

    return true;
}