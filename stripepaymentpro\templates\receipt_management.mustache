{{!
    @template enrol_stripepaymentpro/receipt_management

    Template for receipt URL download management interface

    Context variables required for this template:
    * receipts - Array of receipt data
    * hasreceipts - Boolean indicating if there are any receipts

    Example context (json):
    {
        "receipts": [
            {
                "id": 123,
                "coursename": "Course Name",
                "studentname": "<PERSON>",
                "studentemail": "<EMAIL>",
                "receipturl": "https://pay.stripe.com/receipts/...",
                "paymentdate": "2023-01-01 12:00:00",
                "hasreceipturl": true
            }
        ],
        "hasreceipts": true
    }
}}

<div class="receipt-management-container">
    <h2>{{#str}}receipturl_download, enrol_stripepaymentpro{{/str}}</h2>
    
    {{#hasreceipts}}
    <!-- Search and bulk actions functionality -->
    <div class="search-container mb-3">
        <div class="row">
            <div class="col-md-6">
                <form class="form-inline">
                    <select class="form-select mr-sm-2" id="bulk-action-select" aria-label="Bulk Action">
                        <option value="">Bulk Action</option>
                        <option value="download">Download Selected</option>
                    </select>
                    <button type="button" class="btn btn-primary" id="apply-bulk-action">Apply</button>
                </form>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text"
                           id="receipt-search"
                           class="form-control"
                           placeholder="{{#str}}search_receipts_placeholder, enrol_stripepaymentpro{{/str}}"
                           aria-label="{{#str}}search_receipts, enrol_stripepaymentpro{{/str}}">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="button" id="clear-search">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">{{#str}}search_help_text, enrol_stripepaymentpro{{/str}}</small>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="receipts-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-receipts"></th>
                    <th>{{#str}}coursename, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}studentname, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}paymentdate, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}receipturl, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}actions, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#receipts}}
                <tr class="receipt-row"
                    datacoursename="{{coursename}}"
                    data-student-name="{{studentname}}"
                    data-student-email="{{studentemail}}"
                    data-receipt-url="{{receipturl}}">
                    <td><input type="checkbox" class="receipt-checkbox" {{^hasreceipturl}}disabled{{/hasreceipturl}}></td>
                    <td class="course-name-cell">
                        <div class="course-info">
                            <i class="fa fa-graduation-cap mr-2"></i>
                            <strong>{{coursename}}</strong>
                        </div>
                    </td>
                    <td class="student-name-cell">
                        <div class="student-info">
                            <i class="fa fa-user mr-2"></i>
                            <strong>{{studentname}}</strong>
                            <br><small class="text-muted">{{studentemail}}</small>
                        </div>
                    </td>
                    <td class="payment-date-cell">
                        <span class="badge badge-info">{{paymentdate}}</span>
                    </td>
                    <td class="receipt-url-cell">
                        {{#hasreceipturl}}
                        <button class="copy-receipt-url btn btn-sm btn-outline-secondary"
                                data-receipt-url="{{receipturl}}"
                                title="{{#str}}click_to_copy_receipturl, enrol_stripepaymentpro{{/str}}"
                                style="font-family: monospace; padding: 4px 8px;">
                            {{#str}}receipturl_available, enrol_stripepaymentpro{{/str}}
                            <i class="fa fa-copy ml-1" style="font-size: 0.8em;"></i>
                        </button>
                        {{/hasreceipturl}}
                        {{^hasreceipturl}}
                        <span class="text-muted">{{#str}}no_receipturl, enrol_stripepaymentpro{{/str}}</span>
                        {{/hasreceipturl}}
                    </td>
                    <td class="actions-cell">
                        {{#hasreceipturl}}
                        <a href="{{receipturl}}" 
                           target="_blank" 
                           class="btn btn-sm btnprimary download-receipt-btn"
                           title="{{#str}}download_receipt, enrol_stripepaymentpro{{/str}}">
                            <i class="fa fa-download mr-1"></i>
                            {{#str}}download, enrol_stripepaymentpro{{/str}}
                        </a>
                        {{/hasreceipturl}}
                    </td>
                </tr>
                {{/receipts}}
            </tbody>
        </table>
    </div>
    {{/hasreceipts}}

    {{^hasreceipts}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle mr-2"></i>
        {{#str}}no_receipts_found, enrol_stripepaymentpro{{/str}}
    </div>
    {{/hasreceipts}}
</div>

<!-- JavaScript functionality is handled by the receipt_management AMD module -->

