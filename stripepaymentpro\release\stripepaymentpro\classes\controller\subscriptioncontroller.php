<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Subscription management controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use \Stripe\StripeClient;

/**
 * Controller for subscription management functionality
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class subscriptioncontroller {

    /**
     * Stripe client for communication with Stripe
     */
    private $stripe;

    /**
     * Plugin instance
     */
    private $plugin;

    /**
     * Constructor
     */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->stripe = new StripeClient($this->plugin->getcurrentsecretkey());
    }

    /**
     * Get subscriptions for user or all users (admin)
     * 
     * @param int $userid User ID (0 for all users if admin)
     * @param bool $isadmin Whether the user is admin
     * @return array Array of subscription data
     */
    public function getsubscriptions($userid = 0, $isadmin = false) {
        global $DB, $USER;

        $conditions = ['producttype' => 'service'];
        
        if ($isadmin && $userid == 0) {
            // Admin viewing all subscriptions
            $subscriptions = $DB->get_records('enrol_stripepaymentpro', $conditions);
        } else {
            // User viewing their own subscriptions or admin viewing specific user
            $conditions['userid'] = $userid ?: $USER->id;
            $subscriptions = $DB->get_records('enrol_stripepaymentpro', $conditions);
        }

        $stripesubscriptions = [];
        $subscriptionsbyid = [];

        foreach ($subscriptions as $sub) {
            $subscriptionsbyid[$sub->subscriptionid] = $sub;
            try {
                $subscription = $this->stripe->subscriptions->retrieve($sub->subscriptionid);
                $stripesubscriptions[] = $subscription;
            } catch (\Exception $e) {
                \core\notification::error($e->getMessage());
            }
        }

        return [
            'stripesubscriptions' => $stripesubscriptions,
            'subscriptionsbyid' => $subscriptionsbyid
        ];
    }

    /**
     * Cancel a subscription
     * 
     * @param string $subscriptionid Stripe subscription ID
     * @param array $allowedsubscriptions Array of allowed subscription IDs for security
     * @return bool Success status
     */
    public function cancelsubscription($subscriptionid, $allowedsubscriptions = []) {
        if (!in_array($subscriptionid, $allowedsubscriptions)) {
            throw new \moodle_exception('invalidsubid', 'enrol_stripepaymentpro');
        }

        try {
            // Cancel the subscription immediately
            $subscription = $this->stripe->subscriptions->cancel($subscriptionid, ['at_period_end' => false]);

            // Check the status after cancellation
            if ($subscription->status == 'canceled') {
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }
    }

    /**
     * Prepare subscription data for template rendering
     * 
     * @param array $stripesubscriptions Stripe subscription objects
     * @param array $subscriptionsbyid Local subscription data indexed by ID
     * @param bool $isadmin Whether viewing as admin
     * @return array Template data
     */
    public function preparetemplatedata($stripesubscriptions, $subscriptionsbyid, $isadmin = false) {
        global $DB;

        $subscriptiondata = [];

        foreach ($stripesubscriptions as $subscription) {
            $localsub = $subscriptionsbyid[$subscription->id];
            $user = $DB->get_record('user', ['id' => $localsub->userid]);
            $course = $DB->get_record('course', ['id' => $localsub->courseid]);

            // Format status for display and CSS class
            $statusformatted = ucfirst(str_replace('_', ' ', $subscription->status));
            $statusclass = strtolower($subscription->status);

            $data = [
                'subscriptionid' => $subscription->id,
                'coursename' => htmlspecialchars($course->fullname),
                'status' => $statusformatted,
                'statusclass' => $statusclass,
                'startdate' => userdate($subscription->current_period_start, get_string('strftimedatefullshort')),
                'nextpayment' => $subscription->status == 'active' ?
                    userdate($subscription->current_period_end, get_string('strftimedatefullshort')) :
                    get_string('notapplicable', 'enrol_stripepaymentpro'),
                'isactive' => $subscription->status == 'active',
                'cancancel' => $subscription->status == 'active'
            ];

            if ($isadmin) {
                $data['username'] = $user->username;
            }

            $subscriptiondata[] = $data;
        }

        return [
            'subscriptions' => $subscriptiondata,
            'isadmin' => $isadmin,
            'hassubscriptions' => !empty($subscriptiondata)
        ];
    }
}
