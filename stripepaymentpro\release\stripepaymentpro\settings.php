<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe Pro enrolment plugin settings.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_license_controller;
use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_webhook_controller;

defined('MOODLE_INTERNAL') || die();

global $CFG, $ADMIN;

require_once($CFG->dirroot . '/enrol/stripepaymentpro/lib.php');

// Handle license activation/deactivation.
if (optional_param('stripepaymentpro_license_set_status', '', PARAM_RAW)) {
    $license_controller = new enrol_stripepaymentpro_license_controller();
    $licensedata = $license_controller->add_data();
    if (!empty($licensedata)) {
        if (!empty($licensedata->error)) {
            \core\notification::error($licensedata->error);
        } else {
            $remaining = $licensedata->data->activations_remaining;
            $total = $licensedata->data->total_activations_purchased;

            if (!empty($licensedata->deactivated)) {
                $message = "License deactivated successfully. {$remaining} out of {$total} activations remaining. Your webhook endpoint is deleted";
            } else if (!empty($licensedata->activated)) {
                $message = "License activated successfully. {$remaining} out of {$total} activations remaining. Your webhook endpoint is ready";
            }
            \core\notification::success($message);
            //redirect($CFG->wwwroot . '/admin/settings.php?section=stripepaymentpro_license_settings');
        }
    }
}

// Manage subscription menu items using dedicated helper
require_once(__DIR__ . '/classes/helper/menu_helper.php');
use enrol_stripepaymentpro\helper\menu_helper;

// Refresh menu items when accessing settings or during upgrades
if (optional_param('section', '', PARAM_TEXT) == 'enrolsettingsstripepaymentpro' ||
    stripos($_SERVER['REQUEST_URI'], 'upgradesettings.php') !== false) {

    // Refresh subscription menu items
    menu_helper::refreshsubscriptionmenuitems();

    // Handle webhook management for active licenses
    if (get_config('enrol_stripepaymentpro', 'licensestatus') === 'active') {
        // Create webhook endpoint if not exists or recreate if API keys changed.
        $webhookcontroller = new enrol_stripepaymentpro_webhook_controller();
        if (!get_config('enrol_stripepaymentpro', 'stripe_webhook_id')) {
            $webhookcontroller->createwebhook();
        } else {
            // Check if webhook is accessible with current API keys, if not recreate
            try {
                $webhookid = get_config('enrol_stripepaymentpro', 'stripe_webhook_id');
                $pluginpro = enrol_get_plugin('stripepaymentpro');
                $stripeclient = new \Stripe\StripeClient($pluginpro->getcurrentsecretkey());
                $stripeclient->webhookEndpoints->retrieve($webhookid);
            } catch (\Exception $e) {
                // Webhook not accessible with current keys, recreate it
                $webhookcontroller->recreatewebhook();
            }
        }
    }
}

// Set default license status.
if (strpos(get_config('enrol_stripepaymentpro', 'licensestatus'), 'active') === false) {
    set_config("licensestatus", 'inactive', 'enrol_stripepaymentpro');
}

if ($hassiteconfig) {
    // Create settings category.
    $ADMIN->add('enrolments', new admin_category('stripepaymentpro_settings_category',
        get_string('pluginname', 'enrol_stripepaymentpro')));
    $generalsettings = new admin_settingpage('stripepaymentpro_general_settings',
        get_string('generalsettings', 'enrol_stripepaymentpro'), 'moodle/site:config');

    if ($ADMIN->fulltree) {
        $generalsettings->add(new admin_setting_heading(
            'enrol_stripepaymentpro_general_heading',
            '',
            get_string('pluginname_desc', 'enrol_stripepaymentpro')
        ));
        $webservicesoverview = $CFG->wwwroot . '/admin/search.php?query=enablewebservices';
        $restweblink = $CFG->wwwroot . '/admin/settings.php?section=webserviceprotocols';
        $createtoken = $CFG->wwwroot . '/admin/webservice/tokens.php';
        $generalsettings->add(new admin_setting_configtext(
            'enrol_stripepaymentpro/webservice_token',
            get_string('webservicetokenstring', 'enrol_stripepaymentpro'),
            get_string('enablewebservicesfirst', 'enrol_stripepayment') . '<a href="' . $webservicesoverview . '" target="_blank"> '
            . get_string('fromhere', 'enrol_stripepaymentpro') . '</a> . '
            . get_string('createusertoken', 'enrol_stripepaymentpro') . '<a href="' . $restweblink . '" target="_blank"> '
            . get_string('fromhere', 'enrol_stripepaymentpro') . '</a> . '
            . get_string('enabledrestprotocol', 'enrol_stripepaymentpro') . '<a href="' . $createtoken . '" target="_blank"> '
            . get_string('fromhere', 'enrol_stripepaymentpro') . '</a>',
            ''
        ));
        $generalsettings->add(new admin_setting_configselect(
            'enrol_stripepaymentpro/paymentgatewaytype',
            get_string('paymentgatewaytype', 'enrol_stripepaymentpro'),
            get_string('paymentgatewaytype_desc', 'enrol_stripepaymentpro'),
            'checkout',
            [
                'checkout' => get_string('stripecheckout', 'enrol_stripepaymentpro'),
                'elements' => get_string('stripeelements', 'enrol_stripepaymentpro'),
            ]
        ));
        $generalsettings->add(new admin_setting_configcheckbox(
            'enrol_stripepaymentpro/enableautomatictax',
            get_string('enableautomatictax', 'enrol_stripepaymentpro'),
            '',
            0
        ));
    }

    $ADMIN->add('stripepaymentpro_settings_category', $generalsettings);
    $licensesettings = new admin_settingpage('stripepaymentpro_license_settings',
        get_string('licensesetting', 'enrol_stripepaymentpro'), 'moodle/site:config');

    if ($ADMIN->fulltree) {

        $licensesettings->add(new admin_setting_configtext(
            'enrol_stripepaymentpro/apikey',
            get_string('apikey', 'enrol_stripepaymentpro'),
            '<a href="' . get_string('subscriptionurl', 'enrol_stripepaymentpro') . '" target="_blank">' .
            get_string('apikey', 'enrol_stripepaymentpro') . '</a>' . get_string('apikey_desc', 'enrol_stripepaymentpro'),
            '',
            PARAM_TEXT
        ));

        $licensesettings->add(new admin_setting_configtext(
            'enrol_stripepaymentpro/productid',
            get_string('productid', 'enrol_stripepaymentpro'),
            get_string('productid_desc', 'enrol_stripepaymentpro') . ' <a href="' .
            get_string('subscriptionurl', 'enrol_stripepaymentpro') . '" target="_blank">' .
            get_string('productid', 'enrol_stripepaymentpro') . '</a>',
            '',
            PARAM_TEXT
        ));

        $licensesettings->add(new admin_setting_description(
            'enrol_stripepaymentpro/licensestatus',
            get_string('licensestatus', 'enrol_stripepaymentpro'),
            get_config('enrol_stripepaymentpro', 'licensestatus')
        ));

        $orderid = get_config('enrol_stripepaymentpro', 'subscriptionid');
        if (get_config('enrol_stripepaymentpro', 'expirey') === 'When Cancelled') {
            $expirydisplay = get_config('enrol_stripepaymentpro', 'expireyday');
        } else {
            $expirydisplay = get_string('expired', 'enrol_stripepaymentpro') . ' <a href="' .
                get_string('subscriptionrenewurl', 'enrol_stripepaymentpro') . $orderid . '" target="_blank">' .
                get_string('renewnow', 'enrol_stripepaymentpro') . '</a>';
        }

        $licensesettings->add(new admin_setting_description(
            'enrol_stripepaymentpro/expiry',
            get_string('licenseexdate', 'enrol_stripepaymentpro'),
            $expirydisplay
        ));

        $buttontext = (get_config('enrol_stripepaymentpro', 'licensestatus') === 'active')
            ? get_string('deactivelicense', 'enrol_stripepaymentpro')
            : get_string('activelicense', 'enrol_stripepaymentpro');

        $licensesettings->add(new admin_setting_description(
            'enrol_stripepaymentpro/activate_license',
            '',
            '<button type="submit" class="btn btn-primary text-white" name="stripepaymentpro_license_set_status" value="' .
            $buttontext . '">' . $buttontext . '</button>'
        ));
    }

    $ADMIN->add('stripepaymentpro_settings_category', $licensesettings);

    $ADMIN->add('stripepaymentpro_settings_category',
        new admin_externalpage(
            'stripepaymentpro_couponsettings',
            get_string('stripepaymentprocouponmanagementmenuname', 'enrol_stripepaymentpro'),
            new moodle_url('/enrol/stripepaymentpro/coupon_management.php'),
            'moodle/site:config'
        )
    );

    $ADMIN->add('stripepaymentpro_settings_category',
        new admin_externalpage(
            'stripepaymentpro_receipt_settings',
            get_string('stripepaymentpro_receiptdownload_menu_name', 'enrol_stripepaymentpro'),
            new moodle_url('/enrol/stripepaymentpro/receipt_management.php'),
            'moodle/site:config'
        )
    );
}
