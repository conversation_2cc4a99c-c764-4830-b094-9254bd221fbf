<?php

// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin - Simple thank you page (Old Plugin Style)
 *
 * This plugin allows you to set up paid courses.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->libdir . '/enrollib.php');
require_once($CFG->libdir . '/moodlelib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
use \Stripe\Stripe;
use \Stripe\Checkout\Session;
use \Stripe\paymentintent;

// Set up Stripe API
$plugin = enrol_get_plugin('stripepaymentpro');
Stripe::setApiKey($plugin->getcurrentsecretkey());

// Get parameters - support both sessionid and paymentintent
$sessionid = optional_param('session_id', '', PARAM_RAW);
$paymentintentid = optional_param('payment_intent', '', PARAM_RAW);

try {
    if (!empty($sessionid)) {
        // Handle Checkout Session
        $session = Session::retrieve($sessionid);

        // Check if the payment status is 'paid'
        if ($session->payment_status === 'paid') {
            // Retrieve metadata
            $instanceid = $session->metadata->instanceid;
            $userid = $session->metadata->userid;

            // Get the course ID from the enrolment instance
            $enrolinstance = $DB->get_record('enrol', ['id' => $instanceid], '*', MUST_EXIST);
            $course = $DB->get_record('course', ['id' => $enrolinstance->courseid], '*', MUST_EXIST);
            // Redirect to the course view page - NO ENROLLMENT PROCESSING HERE
            redirect(new moodle_url('/course/view.php', ['id' => $course->id]));
        } else {
            // If payment status is not 'paid', display an error message
            echo 'Payment was not successful. Please try again.';
            die();
        }

    } else if (!empty($paymentintentid)) {
        // Handle paymentintent (Elements mode)
        $paymentintent = paymentIntent::retrieve($paymentintentid);

        // Check if the payment status is 'succeeded'
        if ($paymentintent->status === 'succeeded') {
            // Retrieve metadata
            $instanceid = $paymentintent->metadata->instanceid;
            $userid = $paymentintent->metadata->userid;

            // Get the course ID from the enrolment instance
            $enrolinstance = $DB->get_record('enrol', ['id' => $instanceid], '*', MUST_EXIST);
            $course = $DB->get_record('course', ['id' => $enrolinstance->courseid], '*', MUST_EXIST);

            // Redirect to the course view page - NO ENROLLMENT PROCESSING HERE
            redirect(new moodle_url('/course/view.php', ['id' => $course->id]));
        } else {
            // If payment status is not 'succeeded', display an error message
            echo 'Payment was not successful. Please try again.';
            die();
        }

    } else {
        // No valid payment session found
        echo 'Invalid payment session. Please try again.';
        die();
    }

} catch (Exception $e) {
    // Handle error
    \core\notification::error($e->getMessage());
    echo 'Error: ' . $e->getMessage();
    die();
}
