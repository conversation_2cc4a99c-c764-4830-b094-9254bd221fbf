define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default;window.activeCheckoutInstance=null;const r=(e,t,n)=>o([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0],s=e=>{const t=new Map;return{getElement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setElement(e,t){const n=this.getElement(e);n&&(n.innerHTML=t)},toggleElement(e,t){const n=this.getElement(e);n&&(n.style.display=t?"block":"none")},focusElement(e){const t=this.getElement(e);t&&t.focus()},setButton(e,t,n,o=(t?"0.7":"1")){const r=this.getElement(e);r&&(r.disabled=t,r.textContent=n,r.style.opacity=o,r.style.cursor=t?"not-allowed":"pointer")}}};return{stripePaymentPro:function(e,t,n,a,c,i,l,d,u){const m=s(n),p=window.Stripe(a);if(void 0===window.Stripe)return void y("paymentresponse","Stripe.js library not loaded. Please check your template includes.","error");const y=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}m.setElement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),m.toggleElement(e,!0)},g=e=>{m.setElement(e,""),m.toggleElement(e,!1)},h=async e=>{e.preventDefault();const r=m.getElement("coupon"),s=r?.value.trim();if(!s)return y("showmessage",i,"error"),void m.focusElement("coupon");m.setButton("apply",!0,l);try{const e=await((e,t)=>o([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(s,n);if(void 0===e?.status)throw new Error("Invalid server response");t=s,m.toggleElement("coupon",!1),m.toggleElement("apply",!1),(e=>{if(e.message?y("showmessage",e.message,"error"===e.uistate?"error":"success"):g("showmessage"),m.toggleElement("total","paid"===e.uistate||"discount"===e.uistate),"error"!==e.uistate){if(m.toggleElement("discountsection",e.showsections.discountsection),e.showsections.discountsection){if(e.couponname&&m.setElement("discounttag",e.couponname),e.discountamount){const t="USD"===e.currency?"$":e.currency+" ";m.setElement("discountamountdisplay",`-${t}${e.discountamount}`)}if(e.discountamount&&e.discountvalue){const t="USD"===e.currency?"$":e.currency+" ";let n="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${t}${e.discountvalue} off`;e.couponduration&&("repeating"===e.couponduration&&e.coupondurationmonths?n+=` Expires in ${e.coupondurationmonths} months`:"once"!==e.couponduration&&(n+=` ${e.couponduration}`)),m.setElement("discountnote",n)}}if(e.status&&e.currency){const t="USD"===e.currency?"$":e.currency+" ",n=`${t}${parseFloat(e.status).toFixed(2)}`,o=m.getElement("mainprice");o&&(o.textContent=n);const r=m.getElement("totalamount");if(r&&(r.textContent=n),void 0!==e.discountedRenewalFee&&void 0!==e.originalRenewalFee){const n=parseFloat(e.discountedRenewalFee),o=parseFloat(e.originalRenewalFee);n!==o&&([m.getElement("recurring-price-heading"),m.getElement("recurring-price-breakdown")].forEach(e=>{e&&e.textContent.includes(t+o.toFixed(2))&&(e.textContent=e.textContent.replace(t+o.toFixed(2),t+n.toFixed(2)))}),document.querySelectorAll("*").forEach(e=>{e.textContent&&e.textContent.includes("Then "+t+o.toFixed(2))&&(e.textContent=e.textContent.replace("Then "+t+o.toFixed(2),"Then "+t+n.toFixed(2)))}))}}}})(e)}catch(e){y("showmessage",e.message||"Coupon validation failed","error"),m.focusElement("coupon")}finally{m.setButton("apply",!1,"Apply")}},w=async()=>{if(m.getElement("enrolbutton")){g("paymentresponse"),m.setButton("enrolbutton",!0,c);try{const o=await r(e,t,n);o.error?.message?y("paymentresponse",o.error.message,"error"):"success"===o.status&&o.redirecturl?window.location.href=o.redirecturl:y("paymentresponse","Unknown error occurred during payment.","error")}catch(e){y("paymentresponse",e.message,"error")}finally{m.toggleElement("enrolbutton",!1)}}},E=async()=>{const o=m.getElement("payment-element");if(o)try{if(window.activeCheckoutInstance&&window.activeCheckoutInstance.checkout){window.activeCheckoutInstance.checkout.destroy();const e=`payment-element-${window.activeCheckoutInstance.instanceid}`,t=document.getElementById(e);t&&(t.style.display="none");const n=document.getElementById(`load-payment-button-${window.activeCheckoutInstance.instanceid}`);n&&(n.style.display="block")}const s=async()=>{const o=await r(e,t,n);if(o.error&&o.error.message)throw new Error(o.error.message);if(o.paymentintent)try{return("string"==typeof o.paymentintent?JSON.parse(o.paymentintent):o.paymentintent).clientSecret}catch(e){throw console.error("Failed to parse paymentintent:",e),console.error("paymentintent raw data:",o.paymentintent),new Error("Invalid payment data received from server.")}throw new Error("No client secret found in response")};o.innerHTML="";const a=await p.initEmbeddedCheckout({fetchClientSecret:s}),c="payment-element";a.mount(`#${c}-${n}`),window.activeCheckoutInstance={stripe:p,checkout:a,instanceid:n},m.toggleElement("payment-element",!0),m.toggleElement("load-payment-button",!1)}catch(e){console.error("Stripe Elements initialization error:",e),y("paymentresponse",e.message||"Stripe initialization error. Check console.","error"),m.toggleElement("payment-element",!1),m.toggleElement("load-payment-button",!0)}else y("paymentresponse","Payment element container (ID: payment-element) not found in HTML. Check your template.","error")};"elements"===d&&(u?E():(m.toggleElement("payment-element",!1),m.toggleElement("load-payment-button",!0))),(()=>{[{id:"apply",event:"click",handler:h},{id:"enrolbutton",event:"click",handler:w}].forEach(({id:e,event:t,handler:n})=>{const o=m.getElement(e);o&&o.addEventListener(t,n)});const e=m.getElement("load-payment-button");e&&e.addEventListener("click",()=>E())})()},initCouponSettings:()=>{document.querySelectorAll('[data-toggle="dropdown"], [data-bs-toggle="dropdown"]').forEach(e=>{e.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),document.querySelectorAll(".dropdown-menu.show").forEach(e=>{e!==this.nextElementSibling&&(e.classList.remove("show"),e.parentElement.querySelector(".dropdown-toggle").setAttribute("aria-expanded","false"))});const t=this.nextElementSibling;if(t&&t.classList.contains("dropdown-menu")){const e=t.classList.contains("show");t.classList.toggle("show"),this.setAttribute("aria-expanded",e?"false":"true")}})}),document.addEventListener("click",function(e){e.target.closest(".dropdown")||document.querySelectorAll(".dropdown-menu.show").forEach(e=>{e.classList.remove("show");const t=e.parentElement.querySelector(".dropdown-toggle");t&&t.setAttribute("aria-expanded","false")})});const e=document.querySelector(".table-responsive");if(e&&!document.querySelector("#couponsearch")){const t=document.createElement("div");t.style.marginBottom="15px",t.innerHTML='\n            <input type="text" id="couponsearch" placeholder="Search coupons..."\n                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">\n        ',e.parentNode.insertBefore(t,e),document.getElementById("couponsearch").addEventListener("input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".table tbody tr").forEach(e=>{e.style.display=e.textContent.toLowerCase().includes(t)?"":"none"})})}}}});
