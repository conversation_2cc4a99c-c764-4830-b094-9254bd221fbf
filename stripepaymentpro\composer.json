{"name": "moodle/enrol_stripepaymentpro", "description": "Stripe Payment enrolment pro plugin for Moodle", "type": "moodle-enrol", "license": "GPL-3.0-or-later", "homepage": "https://dualcube.com", "authors": [{"name": "DualCube <<EMAIL>>", "homepage": "https://dualcube.com"}], "autoload": {"psr-4": {"enrol_stripepaymentpro\\": "classes/"}}, "minimum-stability": "stable", "prefer-stable": true, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require-dev": {"moodlehq/moodle-cs": "^3.2", "phpcompatibility/php-compatibility": "^9.3", "squizlabs/php_codesniffer": "^3.13"}, "scripts": {"build": "./node_modules/.bin/grunt amd --root=enrol/stripepaymentpro", "copy-files": ["mkdir -p release/stripepaymentpro", "@php copy-release.php"], "copy-files-windows": ["if not exist release mkdir release", "if not exist release\\stripepaymentpro mkdir release\\stripepaymentpro", "@php copy-release.php"], "zip-unix": ["@build", "@copy-files", "cd release && zip -r stripepaymentpro.zip stripepaymentpro -x@../exclude.lst", "rm -rf release/stripepaymentpro"], "git-tag-and-push": ["@php scripts/git-tag-and-push.php"], "phpcs": "vendor/bin/phpcs --standard=Moodle --ignore=vendor/*,.git/* .", "phpcf": "vendor/bin/phpcbf --standard=Moodle --ignore=vendor/*,.git/* ."}}