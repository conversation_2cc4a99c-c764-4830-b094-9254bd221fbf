define(["exports","core/ajax","core/str"],function(e,t,n){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=o(t),r=o(n);let a={};const l=()=>{const e=document.getElementById("couponsearch"),t=document.getElementById("clear-search"),n=document.getElementById("coupons-table");if(!e||!n)return;const o=()=>{const t=e.value.toLowerCase().trim(),o=n.querySelectorAll("tbody tr.coupon-row");let r=0;o.forEach(e=>{const n=e.getAttribute("datacoursename")?.toLowerCase()||"",o=e.getAttribute("datacouponname")?.toLowerCase()||"",s=e.getAttribute("datadiscounttext")?.toLowerCase()||"",a=!t||n.includes(t)||o.includes(t)||s.includes(t);e.style.display=a?"":"none",a&&r++}),s(r,t)},s=(e,t)=>{let o=n.querySelector(".noresultsrow");0===e&&""!==t?(o||(o=document.createElement("tr"),o.className="noresultsrow",o.innerHTML=`<td colspan="4" class="text-center text-muted py-4">\n                    <i class="fa fa-search mr-2"></i>${a.noResults}\n                </td>`,n.querySelector("tbody").appendChild(o)),o.style.display=""):o&&(o.style.display="none")},r=()=>{e.value="",o(),e.focus()};e.addEventListener("input",o),e.addEventListener("keyup",e=>{"Escape"===e.key&&r()}),t&&t.addEventListener("click",r)},c=()=>{document.addEventListener("click",e=>{const t=e.target.closest(".copycouponname");if(!t)return;const n=t.getAttribute("datacouponname");if(!n)return;const o=document.createElement("textarea");o.value=n,document.body.appendChild(o),o.select(),o.setSelectionRange(0,99999);const s=t.innerHTML;try{document.execCommand("copy"),t.innerHTML=`<i class="fa fa-check ml-1" style="font-size: 0.8em; color: green;"></i> ${a.copied}`,t.style.color="green"}catch(e){t.innerHTML=`<i class="fa fa-times ml-1" style="font-size: 0.8em; color: red;"></i> ${a.failed}`,t.style.color="red"}finally{setTimeout(()=>{t.innerHTML=s,t.style.color=""},2e3),document.body.removeChild(o)}})},i=async(e,t,n)=>{if(confirm(a.confirmDelete))try{const o=n.innerHTML;n.innerHTML='<i class="fa fa-spinner fa-spin"></i>',n.disabled=!0;const r={methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:e,couponid:t}},l=await s.default.call([r])[0];l.success?n.closest("tr")?.remove():(n.innerHTML=o,n.disabled=!1,alert(`${a.errorDelete}: ${l.message||"Unknown error"}`))}catch(e){n.innerHTML='<i class="fa fa-trash"></i>',n.disabled=!1,alert(`${a.errorDelete}: ${e.message}`)}},d=async(e,t)=>{if(confirm(a.confirmDeleteAll))try{const n=t.innerHTML;t.innerHTML='<i class="fa fa-spinner fa-spin"></i>',t.disabled=!0;const o={methodname:"moodle_stripepaymentpro_deactivate_all_coupons",args:{courseid:e}},r=await s.default.call([o])[0];r.success?(alert(`${a.success}: ${r.message}`),window.location.reload()):(t.innerHTML=n,t.disabled=!1,alert(`${a.errorDeleteAll}: ${r.message||"Unknown error"}`))}catch(e){t.innerHTML='<i class="fa fa-trash-alt"></i>',t.disabled=!1,alert(`${a.errorDeleteAll}: ${e.message}`)}};window.handleCouponDelete=i,window.handleDeleteAllCoupons=d,e.handleCouponDelete=i,e.handleDeleteAllCoupons=d,e.init=()=>{r.default.get_strings([{key:"nosearchresults",component:"enrol_stripepaymentpro"},{key:"copied",component:"enrol_stripepaymentpro"},{key:"failed",component:"enrol_stripepaymentpro"},{key:"areyousuredeletecoupon",component:"enrol_stripepaymentpro"},{key:"areyousuredeleteallcoupons",component:"enrol_stripepaymentpro"},{key:"errordeletingcoupon",component:"enrol_stripepaymentpro"},{key:"successmessage",component:"enrol_stripepaymentpro"}]).then(e=>{a={noResults:e[0],copied:e[1],failed:e[2],confirmDelete:e[3],confirmDeleteAll:e[4],errorDelete:e[5],errorDeleteAll:e[6],success:e[7]};const t=document.getElementById("allcouponbutton"),n=document.getElementById("generatecouponsbutton"),o=document.getElementById("allcouponssection"),s=document.getElementById("generatecouponsection"),r=()=>{o&&s&&(o.style.display="block",s.style.display="none"),t&&(t.classList.remove("btnsecondary"),t.classList.add("btnprimary","active")),n&&(n.classList.remove("btnprimary","active"),n.classList.add("btnsecondary"))},i=()=>{o&&s&&(o.style.display="none",s.style.display="block"),n&&(n.classList.remove("btnsecondary"),n.classList.add("btnprimary","active")),t&&(t.classList.remove("btnprimary","active"),t.classList.add("btnsecondary"))};t&&t.addEventListener("click",r),n&&n.addEventListener("click",i),r(),l(),c()}).catch(e=>{console.error("Failed to load localized strings:",e)})},Object.defineProperty(e,"__esModule",{value:!0})});
