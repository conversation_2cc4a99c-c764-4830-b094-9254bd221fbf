<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon management controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use \Stripe\StripeClient;

/**
 * Controller for coupon management functionality
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class couponcontroller {

    /**
     * Stripe client for communication with Stripe
     */
    private $stripe;

    /**
     * Plugin instance
     */
    private $plugin;

    /**
     * Core plugin instance
     */
    private $plugincore;

    /**
     * Constructor
     */
    public function __construct() {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepaymentpro');
        $this->stripe = new StripeClient($this->plugin->getcurrentsecretkey());
    }

    /**
     * Get all coupons grouped by course
     *
     * @return array Coupon data for template
     */
    public function get_coupons_data() {
        global $DB;

        // Fetch coupons
        $coupons = $DB->get_records('enrol_stripepaymentpro_coupons');

        // Group coupons by course
        $couponsbycourse = [];
        $couponslist = [];
        $courses = [];

        foreach ($coupons as $record) {
            // Handle empty stripeproductid values consistently
            $productid = $record->stripeproductid !== null ? $record->stripeproductid : '';
            $couponsbycourse[$productid][] = $record->couponid;
            $couponslist[$record->couponid] = $record;
        }

        // Fetch course names - improved logic to handle null/empty product IDs
        $stripeproductids = array_keys($couponsbycourse);

        if (count($stripeproductids) > 0) {
            // Separate non-empty product IDs from empty ones
            $nonemptyproductids = array_filter($stripeproductids, function($id) {
                return !empty($id);
            });

            $courses = [];

            // Handle non-empty product IDs
            if (!empty($nonemptyproductids)) {
                list($insql, $inparams) = $DB->get_in_or_equal($nonemptyproductids);
                $query = "SELECT DISTINCT e.customtext2, c.fullname, c.id as courseid
                            FROM {course} as c
                            JOIN {enrol} as e ON c.id = e.courseid
                           WHERE e.enrol = 'stripepaymentpro'
                             AND e.customtext2 $insql
                             AND e.customtext2 IS NOT NULL
                             AND e.customtext2 != ''";
                $courserecords = $DB->get_records_sql($query, $inparams);

                // Re-index courses by customtext2 (stripeproductid) for proper lookup
                foreach ($courserecords as $course) {
                    $key = $course->customtext2;
                    $courses[$key] = $course->fullname;
                }
            }

            // Handle empty product IDs - these are "All Courses" coupons
            if (in_array('', $stripeproductids)) {
                $courses[''] = "All Courses";
            }

            // Add fallback for any product IDs that weren't found
            foreach ($stripeproductids as $productid) {
                if (!isset($courses[$productid])) {
                    if (empty($productid)) {
                        $courses[$productid] = "All Courses";
                    } else {
                        $courses[$productid] = "Unknown Course (Product ID: " . $productid . ")";
                    }
                }
            }
        }

        return [
            'couponsbycourse' => $couponsbycourse,
            'couponslist' => $couponslist,
            'courses' => $courses,
            'hascoupons' => !empty($couponsbycourse)
        ];
    }

    /**
     * Get courses with stripepaymentpro enrollment method
     *
     * @return array Course list for form
     */
    public function get_stripe_courses() {
        global $DB;

        $courseswithstripe = $DB->get_records_sql(
            "SELECT DISTINCT e.customtext2, c.fullname
                        FROM {enrol} e
                        JOIN {course} c ON e.courseid = c.id
                       WHERE e.enrol = 'stripepaymentpro'"
        );

        $stripecourselist = [];
        foreach ($courseswithstripe as $course) {
            $stripecourselist[$course->customtext2] = $course->fullname;
        }

        return $stripecourselist;
    }

    /**
     * Create a new coupon
     *
     * @param object $formdata Form data from coupon creation form
     * @return bool Success status
     */
    public function create_coupon($formdata) {
        global $DB;

        $stripecreatecouponparams = [];

        $stripecreatecouponparams['name'] = $formdata->couponname;

        if ($formdata->coupon_expiry > time()) {
            $stripecreatecouponparams['redeem_by'] = $formdata->couponexpiry;
        }

        if ($formdata->coupontypes == "amountoff") {
            $stripecreatecouponparams['amount_off'] = intval($formdata->discountamount) *
                $this->plugin->get_fractional_unit_amount($formdata->coupon_currency);
            $stripecreatecouponparams['currency'] = $formdata->coupon_currency;
        } else if ($formdata->coupontypes == "percentoff") {
            $stripecreatecouponparams['percent_off'] = $formdata->discountamount;
        }

        $stripecreatecouponparams['duration'] = $formdata->coupon_duration;

        if ($formdata->coupon_duration == "repeating") {
            $stripecreatecouponparams['duration_in_months'] = $formdata->coupon_duration_multiple_months_val;
        }

        if ($formdata->coupon_course_assignment != 0) {
            $stripecreatecouponparams['applies_to']['products'] = [$formdata->coupon_course_assignment];
        }

        try {
            $coupon = $this->stripe->coupons->create($stripecreatecouponparams);
            $stripepromotioncodeparams = [];
            $stripepromotioncodeparams['coupon'] = $coupon->id;
            $stripepromotioncodeparams['code'] = $coupon->name;
            $stripepromotioncodeparams['active'] = true;
            $this->stripe->promotionCodes->create($stripepromotioncodeparams);

            if ($coupon != null && $coupon->id != "") {
                $record = new \stdClass();
                $record->couponid = $coupon->id;
                $record->couponname = $coupon->name;
                if (!is_null($coupon->amount_off) && !is_null($coupon->currency)) {
                    $record->amountoff = ($coupon->amount_off / $this->plugin->get_fractional_unit_amount(strtoupper($coupon->currency)));
                    $record->currency = $coupon->currency;
                } else {
                    $record->amountoff = null;
                    $record->currency = null;
                }
                $record->percentoff = $coupon->percent_off ?? null;
                $record->duration = $coupon->duration;
                $record->noofmonths = $coupon->duration_in_months ? $coupon->duration_in_months : 0;
                $record->stripeproduct_id = $formdata->coupon_course_assignment;
                $record->timecreated = $coupon->created;
                $record->couponexpiry = $coupon->redeem_by ? $coupon->redeem_by : 0;

                if (!$DB->record_exists('enrol_stripepaymentpro_coupons', ['couponid' => $record->couponid])) {
                    $DB->insert_record('enrol_stripepaymentpro_coupons', $record);
                    return true;
                } else {
                    \core\notification::error(get_string('duplicatedata', 'enrol_stripepaymentpro'));
                    return false;
                }
            }
        } catch (\Exception $e) {
            \core\notification::error($e->getMessage());
            return false;
        }

        return false;
    }

    /**
     * Prepare coupon data for template rendering
     *
     * @param array $couponsbycourse Coupons grouped by course
     * @param array $couponslist All coupons data
     * @param array $courses Course data
     * @return array Template data
     */
    public function preparecoupontemplatedata($couponsbycourse, $couponslist, $courses) {
        $templatedata = [];

        foreach ($couponsbycourse as $courseid => $coupons) {
            // Get course name from the courses array
            $coursename = isset($courses[$courseid]) ? $courses[$courseid] :
                          (empty($courseid) ? 'All Courses' : 'Unknown Course (Product ID: ' . $courseid . ')');

            $coupondata = [
                'coursename' => htmlspecialchars($coursename),
                'courseid' => $courseid,
                'coupons' => [],
                'hasmultiplecoupons' => count($coupons) > 1
            ];

            foreach ($coupons as $index => $couponid) {
                // Check if coupon exists in the couponslist array
                if (!isset($couponslist[$couponid]) || $couponslist[$couponid] === null) {
                    // Skip this coupon if not found or null
                    continue;
                }

                $coupon = $couponslist[$couponid];
                $discounttext = '';

                if ($coupon->percentoff > 0) {
                    $discounttext = $coupon->percentoff . '% off';
                } else if (isset($coupon->amount_off)) {
                    $discounttext = html_entity_decode($this->plugincore->show_currency_symbol(strtolower($coupon->currency))) .
                        ($coupon->amount_off ) . ' off';
                }

                $durationtext = ($coupon->duration == "repeating") ?
                    " for " . $coupon->no_of_months . " months" : " " . $coupon->duration;

                $expirytext = $coupon->couponexpiry > 0 ?
                    " Expiry: " . userdate($coupon->couponexpiry) : " Expiry: Never";
                $creationdate = userdate( $coupon->timecreated);

                $coupondata['coupons'][] = [
                    'couponid' => $couponid,
                    'couponname' => htmlspecialchars($coupon->couponname ?? 'Unknown Coupon'),
                    'discounttext' => $discounttext,
                    'durationtext' => $durationtext,
                    'expirytext' => $expirytext,
                    'fulldescription' => $discounttext . $durationtext . $expirytext,
                    'isfirstcoupon' => $index === 0,
                    'coursename' => $coupondata['coursename'],
                    'courseid' => $coupondata['courseid'],
                    'totalcoupons' => count($coupons),
                    'creationdate' => $creationdate
                ];
            }

            $templatedata[] = $coupondata;
        }

        return $templatedata;
    }
}
