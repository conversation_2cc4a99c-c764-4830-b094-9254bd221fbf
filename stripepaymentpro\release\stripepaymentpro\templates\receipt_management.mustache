{{!
    @template enrol_stripepaymentpro/receipt_management

    Template for receipt URL download management interface

    Context variables required for this template:
    * receipts - Array of receipt data
    * hasreceipts - Boolean indicating if there are any receipts

    Example context (json):
    {
        "receipts": [
            {
                "id": 123,
                "coursename": "Course Name",
                "studentname": "<PERSON>",
                "studentemail": "<EMAIL>",
                "receipturl": "https://pay.stripe.com/receipts/...",
                "paymentdate": "2023-01-01 12:00:00",
                "hasreceipturl": true
            }
        ],
        "hasreceipts": true
    }
}}

<div class="receipt-management-container">
    <h2>{{#str}}receipturl_download, enrol_stripepaymentpro{{/str}}</h2>
    
    {{#hasreceipts}}
    <!-- Search functionality -->
    <div class="search-container mb-3">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text"
                           id="receipt-search"
                           class="form-control"
                           placeholder="{{#str}}search_receipts_placeholder, enrol_stripepaymentpro{{/str}}"
                           aria-label="{{#str}}search_receipts, enrol_stripepaymentpro{{/str}}">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="button" id="clear-search">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <small class="form-text text-muted">{{#str}}search_help_text, enrol_stripepaymentpro{{/str}}</small>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="receipts-table">
            <thead>
                <tr>
                    <th>{{#str}}coursename, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}studentname, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}paymentdate, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}receipturl, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}actions, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#receipts}}
                <tr class="receipt-row"
                    datacoursename="{{coursename}}"
                    data-student-name="{{studentname}}"
                    data-student-email="{{studentemail}}">
                    <td class="course-name-cell">
                        <div class="course-info">
                            <i class="fa fa-graduation-cap mr-2"></i>
                            <strong>{{coursename}}</strong>
                        </div>
                    </td>
                    <td class="student-name-cell">
                        <div class="student-info">
                            <i class="fa fa-user mr-2"></i>
                            <strong>{{studentname}}</strong>
                            <br><small class="text-muted">{{studentemail}}</small>
                        </div>
                    </td>
                    <td class="payment-date-cell">
                        <span class="badge badge-info">{{paymentdate}}</span>
                    </td>
                    <td class="receipt-url-cell">
                        {{#hasreceipturl}}
                        <button class="copy-receipt-url btn btn-sm btn-outline-secondary"
                                data-receipt-url="{{receipturl}}"
                                title="{{#str}}click_to_copy_receipturl, enrol_stripepaymentpro{{/str}}"
                                style="font-family: monospace; padding: 4px 8px;">
                            {{#str}}receipturl_available, enrol_stripepaymentpro{{/str}}
                            <i class="fa fa-copy ml-1" style="font-size: 0.8em;"></i>
                        </button>
                        {{/hasreceipturl}}
                        {{^hasreceipturl}}
                        <span class="text-muted">{{#str}}no_receipturl, enrol_stripepaymentpro{{/str}}</span>
                        {{/hasreceipturl}}
                    </td>
                    <td class="actions-cell">
                        {{#hasreceipturl}}
                        <a href="{{receipturl}}" 
                           target="_blank" 
                           class="btn btn-sm btnprimary download-receipt-btn"
                           title="{{#str}}download_receipt, enrol_stripepaymentpro{{/str}}">
                            <i class="fa fa-download mr-1"></i>
                            {{#str}}download, enrol_stripepaymentpro{{/str}}
                        </a>
                        {{/hasreceipturl}}
                    </td>
                </tr>
                {{/receipts}}
            </tbody>
        </table>
    </div>
    {{/hasreceipts}}

    {{^hasreceipts}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle mr-2"></i>
        {{#str}}no_receipts_found, enrol_stripepaymentpro{{/str}}
    </div>
    {{/hasreceipts}}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('receipt-search');
    const clearButton = document.getElementById('clear-search');
    const table = document.getElementById('receipts-table');
    
    if (searchInput && table) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(function(row) {
                const courseName = row.dataset.courseName.toLowerCase();
                const studentName = row.dataset.studentName.toLowerCase();
                const studentEmail = row.dataset.studentEmail.toLowerCase();
                
                const matches = courseName.includes(searchTerm) || 
                               studentName.includes(searchTerm) || 
                               studentEmail.includes(searchTerm);
                
                row.style.display = matches ? '' : 'none';
            });
        });
    }
    
    if (clearButton && searchInput) {
        clearButton.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
        });
    }
    
    // Copy receipt URL functionality
    document.querySelectorAll('.copy-receipt-url').forEach(function(button) {
        button.addEventListener('click', function() {
            const receiptUrl = this.dataset.receiptUrl;
            
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(receiptUrl).then(function() {
                    showCopySuccess(button);
                }).catch(function() {
                    fallbackCopyTextToClipboard(receiptUrl, button);
                });
            } else {
                fallbackCopyTextToClipboard(receiptUrl, button);
            }
        });
    });
    
    function showCopySuccess(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fa fa-check text-success"></i> {{#str}}copied, enrol_stripepaymentpro{{/str}}';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }
    
    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showCopySuccess(button);
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
        }
        
        document.body.removeChild(textArea);
    }
});
</script>
