{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/mysubscription

    Template for displaying user subscriptions

    Context variables required for this template:
    * subscriptions - Array of subscription data
    * isadmin - <PERSON>olean indicating if user is admin

    Example context (json):
    {
        "subscriptions": [
            {
                "subscriptionid": "sub_123",
                "coursename": "Course Name",
                "username": "user123",
                "status": "active",
                "startdate": "2023-01-01 00:00:00",
                "nextpayment": "2023-02-01 00:00:00",
                "isactive": true,
                "cancancel": true
            }
        ],
        "isadmin": true,
        "hassubscriptions": true
    }
}}

<div class="subscription-management-container">
    {{#hassubscriptions}}
    <div class="subscription-table-wrapper">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{{#str}}coursename, enrol_stripepaymentpro{{/str}}</th>
                    {{#isadmin}}
                    <th>{{#str}}subid, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}username, enrol_stripepaymentpro{{/str}}</th>
                    {{/isadmin}}
                    <th>{{#str}}status, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}startdate, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}nextpayment, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}action, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#subscriptions}}
                <tr>
                    <td>
                        <div class="subscription-course-name" title="{{coursename}}">
                            {{coursename}}
                        </div>
                    </td>
                    {{#isadmin}}
                    <td>
                        <code>{{subscriptionid}}</code>
                    </td>
                    <td>{{username}}</td>
                    {{/isadmin}}
                    <td>
                        <span class="subscription-status {{statusclass}}">{{status}}</span>
                    </td>
                    <td>
                        <span class="subscription-date">{{startdate}}</span>
                    </td>
                    <td>
                        <span class="subscription-date">{{nextpayment}}</span>
                    </td>
                    <td>
                        {{#cancancel}}
                        <a class="subscription-action-btn btn-cancel"
                           onclick="return confirm('{{#str}}areyousure, enrol_stripepaymentpro{{/str}}\n{{#str}}areyousure_des, enrol_stripepaymentpro{{/str}}')"
                           href="?subid={{subscriptionid}}"
                           title="{{#str}}cancelsubcription, enrol_stripepaymentpro{{/str}}">
                            <i class="fa fa-times" aria-hidden="true"></i>
                            {{#str}}cancelsubcription, enrol_stripepaymentpro{{/str}}
                        </a>
                        {{/cancancel}}
                        {{^cancancel}}
                        <span class="subscription-inactive-text">
                            {{#str}}notactive, enrol_stripepaymentpro{{/str}}
                        </span>
                        {{/cancancel}}
                    </td>
                </tr>
                {{/subscriptions}}
            </tbody>
        </table>
    </div>
    {{/hassubscriptions}}

    {{^hassubscriptions}}
    <div class="no-subscriptions-message">
        <i class="fa fa-calendar-times-o" aria-hidden="true"></i>
        <p>{{#str}}nosubscriptionsfound, enrol_stripepaymentpro{{/str}}</p>
    </div>
    {{/hassubscriptions}}
</div>