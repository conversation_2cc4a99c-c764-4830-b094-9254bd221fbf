/*
* Underscore.string
* (c) 2010 Esa-<PERSON><PERSON> <esa-matti aet suuronen dot org>
* Underscore.string is freely distributable under the terms of the MIT license.
* Documentation: https://github.com/epeli/underscore.string
* Some code is borrowed from MooTools and <PERSON><PERSON><PERSON>.
* Version '3.3.6'
* @preserve
*/
(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.s=f()}})(function(){var define,module,exports;return function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r}()({1:[function(require,module,exports){var trim=require("./trim");var decap=require("./decapitalize");module.exports=function camelize(str,decapitalize){str=trim(str).replace(/[-_\s]+(.)?/g,function(match,c){return c?c.toUpperCase():""});if(decapitalize===true){return decap(str)}else{return str}}},{"./decapitalize":10,"./trim":65}],2:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function capitalize(str,lowercaseRest){str=makeString(str);var remainingChars=!lowercaseRest?str.slice(1):str.slice(1).toLowerCase();return str.charAt(0).toUpperCase()+remainingChars}},{"./helper/makeString":20}],3:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function chars(str){return makeString(str).split("")}},{"./helper/makeString":20}],4:[function(require,module,exports){module.exports=function chop(str,step){if(str==null)return[];str=String(str);step=~~step;return step>0?str.match(new RegExp(".{1,"+step+"}","g")):[str]}},{}],5:[function(require,module,exports){var capitalize=require("./capitalize");var camelize=require("./camelize");var makeString=require("./helper/makeString");module.exports=function classify(str){str=makeString(str);return capitalize(camelize(str.replace(/[\W_]/g," ")).replace(/\s/g,""))}},{"./camelize":1,"./capitalize":2,"./helper/makeString":20}],6:[function(require,module,exports){var trim=require("./trim");module.exports=function clean(str){return trim(str).replace(/\s\s+/g," ")}},{"./trim":65}],7:[function(require,module,exports){var makeString=require("./helper/makeString");var from="ąàáäâãåæăćčĉęèéëêĝĥìíïîĵłľńňòóöőôõðøśșşšŝťțţŭùúüűûñÿýçżźž",to="aaaaaaaaaccceeeeeghiiiijllnnoooooooossssstttuuuuuunyyczzz";from+=from.toUpperCase();to+=to.toUpperCase();to=to.split("");from+="ß";to.push("ss");module.exports=function cleanDiacritics(str){return makeString(str).replace(/.{1}/g,function(c){var index=from.indexOf(c);return index===-1?c:to[index]})}},{"./helper/makeString":20}],8:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function(str,substr){str=makeString(str);substr=makeString(substr);if(str.length===0||substr.length===0)return 0;return str.split(substr).length-1}},{"./helper/makeString":20}],9:[function(require,module,exports){var trim=require("./trim");module.exports=function dasherize(str){return trim(str).replace(/([A-Z])/g,"-$1").replace(/[-_\s]+/g,"-").toLowerCase()}},{"./trim":65}],10:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function decapitalize(str){str=makeString(str);return str.charAt(0).toLowerCase()+str.slice(1)}},{"./helper/makeString":20}],11:[function(require,module,exports){var makeString=require("./helper/makeString");function getIndent(str){var matches=str.match(/^[\s\\t]*/gm);var indent=matches[0].length;for(var i=1;i<matches.length;i++){indent=Math.min(matches[i].length,indent)}return indent}module.exports=function dedent(str,pattern){str=makeString(str);var indent=getIndent(str);var reg;if(indent===0)return str;if(typeof pattern==="string"){reg=new RegExp("^"+pattern,"gm")}else{reg=new RegExp("^[ \\t]{"+indent+"}","gm")}return str.replace(reg,"")}},{"./helper/makeString":20}],12:[function(require,module,exports){var makeString=require("./helper/makeString");var toPositive=require("./helper/toPositive");module.exports=function endsWith(str,ends,position){str=makeString(str);ends=""+ends;if(typeof position=="undefined"){position=str.length-ends.length}else{position=Math.min(toPositive(position),str.length)-ends.length}return position>=0&&str.indexOf(ends,position)===position}},{"./helper/makeString":20,"./helper/toPositive":22}],13:[function(require,module,exports){var makeString=require("./helper/makeString");var escapeChars=require("./helper/escapeChars");var regexString="[";for(var key in escapeChars){regexString+=key}regexString+="]";var regex=new RegExp(regexString,"g");module.exports=function escapeHTML(str){return makeString(str).replace(regex,function(m){return"&"+escapeChars[m]+";"})}},{"./helper/escapeChars":17,"./helper/makeString":20}],14:[function(require,module,exports){module.exports=function(){var result={};for(var prop in this){if(!this.hasOwnProperty(prop)||prop.match(/^(?:include|contains|reverse|join|map|wrap)$/))continue;result[prop]=this[prop]}return result}},{}],15:[function(require,module,exports){var makeString=require("./makeString");module.exports=function adjacent(str,direction){str=makeString(str);if(str.length===0){return""}return str.slice(0,-1)+String.fromCharCode(str.charCodeAt(str.length-1)+direction)}},{"./makeString":20}],16:[function(require,module,exports){var escapeRegExp=require("./escapeRegExp");module.exports=function defaultToWhiteSpace(characters){if(characters==null)return"\\s";else if(characters.source)return characters.source;else return"["+escapeRegExp(characters)+"]"}},{"./escapeRegExp":18}],17:[function(require,module,exports){var escapeChars={"¢":"cent","£":"pound","¥":"yen","€":"euro","©":"copy","®":"reg","<":"lt",">":"gt",'"':"quot","&":"amp","'":"#39"};module.exports=escapeChars},{}],18:[function(require,module,exports){var makeString=require("./makeString");module.exports=function escapeRegExp(str){return makeString(str).replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},{"./makeString":20}],19:[function(require,module,exports){var htmlEntities={nbsp:" ",cent:"¢",pound:"£",yen:"¥",euro:"€",copy:"©",reg:"®",lt:"<",gt:">",quot:'"',amp:"&",apos:"'"};module.exports=htmlEntities},{}],20:[function(require,module,exports){module.exports=function makeString(object){if(object==null)return"";return""+object}},{}],21:[function(require,module,exports){module.exports=function strRepeat(str,qty){if(qty<1)return"";var result="";while(qty>0){if(qty&1)result+=str;qty>>=1,str+=str}return result}},{}],22:[function(require,module,exports){module.exports=function toPositive(number){return number<0?0:+number||0}},{}],23:[function(require,module,exports){var capitalize=require("./capitalize");var underscored=require("./underscored");var trim=require("./trim");module.exports=function humanize(str){return capitalize(trim(underscored(str).replace(/_id$/,"").replace(/_/g," ")))}},{"./capitalize":2,"./trim":65,"./underscored":67}],24:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function include(str,needle){if(needle==="")return true;return makeString(str).indexOf(needle)!==-1}},{"./helper/makeString":20}],25:[function(require,module,exports){
/*
* Underscore.string
* (c) 2010 Esa-Matti Suuronen <esa-matti aet suuronen dot org>
* Underscore.string is freely distributable under the terms of the MIT license.
* Documentation: https://github.com/epeli/underscore.string
* Some code is borrowed from MooTools and Alexandru Marasteanu.
* Version '3.3.6'
* @preserve
*/
"use strict";function s(value){if(!(this instanceof s))return new s(value);this._wrapped=value}s.VERSION="3.3.6";s.isBlank=require("./isBlank");s.stripTags=require("./stripTags");s.capitalize=require("./capitalize");s.decapitalize=require("./decapitalize");s.chop=require("./chop");s.trim=require("./trim");s.clean=require("./clean");s.cleanDiacritics=require("./cleanDiacritics");s.count=require("./count");s.chars=require("./chars");s.swapCase=require("./swapCase");s.escapeHTML=require("./escapeHTML");s.unescapeHTML=require("./unescapeHTML");s.splice=require("./splice");s.insert=require("./insert");s.replaceAll=require("./replaceAll");s.include=require("./include");s.join=require("./join");s.lines=require("./lines");s.dedent=require("./dedent");s.reverse=require("./reverse");s.startsWith=require("./startsWith");s.endsWith=require("./endsWith");s.pred=require("./pred");s.succ=require("./succ");s.titleize=require("./titleize");s.camelize=require("./camelize");s.underscored=require("./underscored");s.dasherize=require("./dasherize");s.classify=require("./classify");s.humanize=require("./humanize");s.ltrim=require("./ltrim");s.rtrim=require("./rtrim");s.truncate=require("./truncate");s.prune=require("./prune");s.words=require("./words");s.pad=require("./pad");s.lpad=require("./lpad");s.rpad=require("./rpad");s.lrpad=require("./lrpad");s.sprintf=require("./sprintf");s.vsprintf=require("./vsprintf");s.toNumber=require("./toNumber");s.numberFormat=require("./numberFormat");s.strRight=require("./strRight");s.strRightBack=require("./strRightBack");s.strLeft=require("./strLeft");s.strLeftBack=require("./strLeftBack");s.toSentence=require("./toSentence");s.toSentenceSerial=require("./toSentenceSerial");s.slugify=require("./slugify");s.surround=require("./surround");s.quote=require("./quote");s.unquote=require("./unquote");s.repeat=require("./repeat");s.naturalCmp=require("./naturalCmp");s.levenshtein=require("./levenshtein");s.toBoolean=require("./toBoolean");s.exports=require("./exports");s.escapeRegExp=require("./helper/escapeRegExp");s.wrap=require("./wrap");s.map=require("./map");s.strip=s.trim;s.lstrip=s.ltrim;s.rstrip=s.rtrim;s.center=s.lrpad;s.rjust=s.lpad;s.ljust=s.rpad;s.contains=s.include;s.q=s.quote;s.toBool=s.toBoolean;s.camelcase=s.camelize;s.mapChars=s.map;s.prototype={value:function value(){return this._wrapped}};function fn2method(key,fn){if(typeof fn!=="function")return;s.prototype[key]=function(){var args=[this._wrapped].concat(Array.prototype.slice.call(arguments));var res=fn.apply(null,args);return typeof res==="string"?new s(res):res}}for(var key in s)fn2method(key,s[key]);fn2method("tap",function tap(string,fn){return fn(string)});function prototype2method(methodName){fn2method(methodName,function(context){var args=Array.prototype.slice.call(arguments,1);return String.prototype[methodName].apply(context,args)})}var prototypeMethods=["toUpperCase","toLowerCase","split","replace","slice","substring","substr","concat"];for(var method in prototypeMethods)prototype2method(prototypeMethods[method]);module.exports=s},{"./camelize":1,"./capitalize":2,"./chars":3,"./chop":4,"./classify":5,"./clean":6,"./cleanDiacritics":7,"./count":8,"./dasherize":9,"./decapitalize":10,"./dedent":11,"./endsWith":12,"./escapeHTML":13,"./exports":14,"./helper/escapeRegExp":18,"./humanize":23,"./include":24,"./insert":26,"./isBlank":27,"./join":28,"./levenshtein":29,"./lines":30,"./lpad":31,"./lrpad":32,"./ltrim":33,"./map":34,"./naturalCmp":35,"./numberFormat":38,"./pad":39,"./pred":40,"./prune":41,"./quote":42,"./repeat":43,"./replaceAll":44,"./reverse":45,"./rpad":46,"./rtrim":47,"./slugify":48,"./splice":49,"./sprintf":50,"./startsWith":51,"./strLeft":52,"./strLeftBack":53,"./strRight":54,"./strRightBack":55,"./stripTags":56,"./succ":57,"./surround":58,"./swapCase":59,"./titleize":60,"./toBoolean":61,"./toNumber":62,"./toSentence":63,"./toSentenceSerial":64,"./trim":65,"./truncate":66,"./underscored":67,"./unescapeHTML":68,"./unquote":69,"./vsprintf":70,"./words":71,"./wrap":72}],26:[function(require,module,exports){var splice=require("./splice");module.exports=function insert(str,i,substr){return splice(str,i,0,substr)}},{"./splice":49}],27:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function isBlank(str){return/^\s*$/.test(makeString(str))}},{"./helper/makeString":20}],28:[function(require,module,exports){var makeString=require("./helper/makeString");var slice=[].slice;module.exports=function join(){var args=slice.call(arguments),separator=args.shift();return args.join(makeString(separator))}},{"./helper/makeString":20}],29:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function levenshtein(str1,str2){"use strict";str1=makeString(str1);str2=makeString(str2);if(str1===str2)return 0;if(!str1||!str2)return Math.max(str1.length,str2.length);var prevRow=new Array(str2.length+1);for(var i=0;i<prevRow.length;++i){prevRow[i]=i}for(i=0;i<str1.length;++i){var nextCol=i+1;for(var j=0;j<str2.length;++j){var curCol=nextCol;nextCol=prevRow[j]+(str1.charAt(i)===str2.charAt(j)?0:1);var tmp=curCol+1;if(nextCol>tmp){nextCol=tmp}tmp=prevRow[j+1]+1;if(nextCol>tmp){nextCol=tmp}prevRow[j]=curCol}prevRow[j]=nextCol}return nextCol}},{"./helper/makeString":20}],30:[function(require,module,exports){module.exports=function lines(str){if(str==null)return[];return String(str).split(/\r\n?|\n/)}},{}],31:[function(require,module,exports){var pad=require("./pad");module.exports=function lpad(str,length,padStr){return pad(str,length,padStr)}},{"./pad":39}],32:[function(require,module,exports){var pad=require("./pad");module.exports=function lrpad(str,length,padStr){return pad(str,length,padStr,"both")}},{"./pad":39}],33:[function(require,module,exports){var makeString=require("./helper/makeString");var defaultToWhiteSpace=require("./helper/defaultToWhiteSpace");var nativeTrimLeft=String.prototype.trimLeft;module.exports=function ltrim(str,characters){str=makeString(str);if(!characters&&nativeTrimLeft)return nativeTrimLeft.call(str);characters=defaultToWhiteSpace(characters);return str.replace(new RegExp("^"+characters+"+"),"")}},{"./helper/defaultToWhiteSpace":16,"./helper/makeString":20}],34:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function(str,callback){str=makeString(str);if(str.length===0||typeof callback!=="function")return str;return str.replace(/./g,callback)}},{"./helper/makeString":20}],35:[function(require,module,exports){module.exports=function naturalCmp(str1,str2){if(str1==str2)return 0;if(!str1)return-1;if(!str2)return 1;var cmpRegex=/(\.\d+|\d+|\D+)/g,tokens1=String(str1).match(cmpRegex),tokens2=String(str2).match(cmpRegex),count=Math.min(tokens1.length,tokens2.length);for(var i=0;i<count;i++){var a=tokens1[i],b=tokens2[i];if(a!==b){var num1=+a;var num2=+b;if(num1===num1&&num2===num2){return num1>num2?1:-1}return a<b?-1:1}}if(tokens1.length!=tokens2.length)return tokens1.length-tokens2.length;return str1<str2?-1:1}},{}],36:[function(require,module,exports){!function(){"use strict";var re={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function sprintf(key){return sprintf_format(sprintf_parse(key),arguments)}function vsprintf(fmt,argv){return sprintf.apply(null,[fmt].concat(argv||[]))}function sprintf_format(parse_tree,argv){var cursor=1,tree_length=parse_tree.length,arg,output="",i,k,ph,pad,pad_character,pad_length,is_positive,sign;for(i=0;i<tree_length;i++){if(typeof parse_tree[i]==="string"){output+=parse_tree[i]}else if(typeof parse_tree[i]==="object"){ph=parse_tree[i];if(ph.keys){arg=argv[cursor];for(k=0;k<ph.keys.length;k++){if(arg==undefined){throw new Error(sprintf('[sprintf] Cannot access property "%s" of undefined value "%s"',ph.keys[k],ph.keys[k-1]))}arg=arg[ph.keys[k]]}}else if(ph.param_no){arg=argv[ph.param_no]}else{arg=argv[cursor++]}if(re.not_type.test(ph.type)&&re.not_primitive.test(ph.type)&&arg instanceof Function){arg=arg()}if(re.numeric_arg.test(ph.type)&&(typeof arg!=="number"&&isNaN(arg))){throw new TypeError(sprintf("[sprintf] expecting number but found %T",arg))}if(re.number.test(ph.type)){is_positive=arg>=0}switch(ph.type){case"b":arg=parseInt(arg,10).toString(2);break;case"c":arg=String.fromCharCode(parseInt(arg,10));break;case"d":case"i":arg=parseInt(arg,10);break;case"j":arg=JSON.stringify(arg,null,ph.width?parseInt(ph.width):0);break;case"e":arg=ph.precision?parseFloat(arg).toExponential(ph.precision):parseFloat(arg).toExponential();break;case"f":arg=ph.precision?parseFloat(arg).toFixed(ph.precision):parseFloat(arg);break;case"g":arg=ph.precision?String(Number(arg.toPrecision(ph.precision))):parseFloat(arg);break;case"o":arg=(parseInt(arg,10)>>>0).toString(8);break;case"s":arg=String(arg);arg=ph.precision?arg.substring(0,ph.precision):arg;break;case"t":arg=String(!!arg);arg=ph.precision?arg.substring(0,ph.precision):arg;break;case"T":arg=Object.prototype.toString.call(arg).slice(8,-1).toLowerCase();arg=ph.precision?arg.substring(0,ph.precision):arg;break;case"u":arg=parseInt(arg,10)>>>0;break;case"v":arg=arg.valueOf();arg=ph.precision?arg.substring(0,ph.precision):arg;break;case"x":arg=(parseInt(arg,10)>>>0).toString(16);break;case"X":arg=(parseInt(arg,10)>>>0).toString(16).toUpperCase();break}if(re.json.test(ph.type)){output+=arg}else{if(re.number.test(ph.type)&&(!is_positive||ph.sign)){sign=is_positive?"+":"-";arg=arg.toString().replace(re.sign,"")}else{sign=""}pad_character=ph.pad_char?ph.pad_char==="0"?"0":ph.pad_char.charAt(1):" ";pad_length=ph.width-(sign+arg).length;pad=ph.width?pad_length>0?pad_character.repeat(pad_length):"":"";output+=ph.align?sign+arg+pad:pad_character==="0"?sign+pad+arg:pad+sign+arg}}}return output}var sprintf_cache=Object.create(null);function sprintf_parse(fmt){if(sprintf_cache[fmt]){return sprintf_cache[fmt]}var _fmt=fmt,match,parse_tree=[],arg_names=0;while(_fmt){if((match=re.text.exec(_fmt))!==null){parse_tree.push(match[0])}else if((match=re.modulo.exec(_fmt))!==null){parse_tree.push("%")}else if((match=re.placeholder.exec(_fmt))!==null){if(match[2]){arg_names|=1;var field_list=[],replacement_field=match[2],field_match=[];if((field_match=re.key.exec(replacement_field))!==null){field_list.push(field_match[1]);while((replacement_field=replacement_field.substring(field_match[0].length))!==""){if((field_match=re.key_access.exec(replacement_field))!==null){field_list.push(field_match[1])}else if((field_match=re.index_access.exec(replacement_field))!==null){field_list.push(field_match[1])}else{throw new SyntaxError("[sprintf] failed to parse named argument key")}}}else{throw new SyntaxError("[sprintf] failed to parse named argument key")}match[2]=field_list}else{arg_names|=2}if(arg_names===3){throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported")}parse_tree.push({placeholder:match[0],param_no:match[1],keys:match[2],sign:match[3],pad_char:match[4],align:match[5],width:match[6],precision:match[7],type:match[8]})}else{throw new SyntaxError("[sprintf] unexpected placeholder")}_fmt=_fmt.substring(match[0].length)}return sprintf_cache[fmt]=parse_tree}if(typeof exports!=="undefined"){exports["sprintf"]=sprintf;exports["vsprintf"]=vsprintf}if(typeof window!=="undefined"){window["sprintf"]=sprintf;window["vsprintf"]=vsprintf;if(typeof define==="function"&&define["amd"]){define(function(){return{sprintf:sprintf,vsprintf:vsprintf}})}}}()},{}],37:[function(require,module,exports){(function(global){(function(){module.exports=deprecate;function deprecate(fn,msg){if(config("noDeprecation")){return fn}var warned=false;function deprecated(){if(!warned){if(config("throwDeprecation")){throw new Error(msg)}else if(config("traceDeprecation")){console.trace(msg)}else{console.warn(msg)}warned=true}return fn.apply(this,arguments)}return deprecated}function config(name){try{if(!global.localStorage)return false}catch(_){return false}var val=global.localStorage[name];if(null==val)return false;return String(val).toLowerCase()==="true"}}).call(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],38:[function(require,module,exports){module.exports=function numberFormat(number,dec,dsep,tsep){if(isNaN(number)||number==null)return"";number=number.toFixed(~~dec);tsep=typeof tsep=="string"?tsep:",";var parts=number.split("."),fnums=parts[0],decimals=parts[1]?(dsep||".")+parts[1]:"";return fnums.replace(/(\d)(?=(?:\d{3})+$)/g,"$1"+tsep)+decimals}},{}],39:[function(require,module,exports){var makeString=require("./helper/makeString");var strRepeat=require("./helper/strRepeat");module.exports=function pad(str,length,padStr,type){str=makeString(str);length=~~length;var padlen=0;if(!padStr)padStr=" ";else if(padStr.length>1)padStr=padStr.charAt(0);switch(type){case"right":padlen=length-str.length;return str+strRepeat(padStr,padlen);case"both":padlen=length-str.length;return strRepeat(padStr,Math.ceil(padlen/2))+str+strRepeat(padStr,Math.floor(padlen/2));default:padlen=length-str.length;return strRepeat(padStr,padlen)+str}}},{"./helper/makeString":20,"./helper/strRepeat":21}],40:[function(require,module,exports){var adjacent=require("./helper/adjacent");module.exports=function succ(str){return adjacent(str,-1)}},{"./helper/adjacent":15}],41:[function(require,module,exports){var makeString=require("./helper/makeString");var rtrim=require("./rtrim");module.exports=function prune(str,length,pruneStr){str=makeString(str);length=~~length;pruneStr=pruneStr!=null?String(pruneStr):"...";if(str.length<=length)return str;var tmpl=function(c){return c.toUpperCase()!==c.toLowerCase()?"A":" "},template=str.slice(0,length+1).replace(/.(?=\W*\w*$)/g,tmpl);if(template.slice(template.length-2).match(/\w\w/))template=template.replace(/\s*\S+$/,"");else template=rtrim(template.slice(0,template.length-1));return(template+pruneStr).length>str.length?str:str.slice(0,template.length)+pruneStr}},{"./helper/makeString":20,"./rtrim":47}],42:[function(require,module,exports){var surround=require("./surround");module.exports=function quote(str,quoteChar){return surround(str,quoteChar||'"')}},{"./surround":58}],43:[function(require,module,exports){var makeString=require("./helper/makeString");var strRepeat=require("./helper/strRepeat");module.exports=function repeat(str,qty,separator){str=makeString(str);qty=~~qty;if(separator==null)return strRepeat(str,qty);for(var repeat=[];qty>0;repeat[--qty]=str){}return repeat.join(separator)}},{"./helper/makeString":20,"./helper/strRepeat":21}],44:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function replaceAll(str,find,replace,ignorecase){var flags=ignorecase===true?"gi":"g";var reg=new RegExp(find,flags);return makeString(str).replace(reg,replace)}},{"./helper/makeString":20}],45:[function(require,module,exports){var chars=require("./chars");module.exports=function reverse(str){return chars(str).reverse().join("")}},{"./chars":3}],46:[function(require,module,exports){var pad=require("./pad");module.exports=function rpad(str,length,padStr){return pad(str,length,padStr,"right")}},{"./pad":39}],47:[function(require,module,exports){var makeString=require("./helper/makeString");var defaultToWhiteSpace=require("./helper/defaultToWhiteSpace");var nativeTrimRight=String.prototype.trimRight;module.exports=function rtrim(str,characters){str=makeString(str);if(!characters&&nativeTrimRight)return nativeTrimRight.call(str);characters=defaultToWhiteSpace(characters);return str.replace(new RegExp(characters+"+$"),"")}},{"./helper/defaultToWhiteSpace":16,"./helper/makeString":20}],48:[function(require,module,exports){var trim=require("./trim");var dasherize=require("./dasherize");var cleanDiacritics=require("./cleanDiacritics");module.exports=function slugify(str){return trim(dasherize(cleanDiacritics(str).replace(/[^\w\s-]/g,"-").toLowerCase()),"-")}},{"./cleanDiacritics":7,"./dasherize":9,"./trim":65}],49:[function(require,module,exports){var chars=require("./chars");module.exports=function splice(str,i,howmany,substr){var arr=chars(str);arr.splice(~~i,~~howmany,substr);return arr.join("")}},{"./chars":3}],50:[function(require,module,exports){var deprecate=require("util-deprecate");module.exports=deprecate(require("sprintf-js").sprintf,"sprintf() will be removed in the next major release, use the sprintf-js package instead.")},{"sprintf-js":36,"util-deprecate":37}],51:[function(require,module,exports){var makeString=require("./helper/makeString");var toPositive=require("./helper/toPositive");module.exports=function startsWith(str,starts,position){str=makeString(str);starts=""+starts;position=position==null?0:Math.min(toPositive(position),str.length);return str.lastIndexOf(starts,position)===position}},{"./helper/makeString":20,"./helper/toPositive":22}],52:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function strLeft(str,sep){str=makeString(str);sep=makeString(sep);var pos=!sep?-1:str.indexOf(sep);return~pos?str.slice(0,pos):str}},{"./helper/makeString":20}],53:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function strLeftBack(str,sep){str=makeString(str);sep=makeString(sep);var pos=str.lastIndexOf(sep);return~pos?str.slice(0,pos):str}},{"./helper/makeString":20}],54:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function strRight(str,sep){str=makeString(str);sep=makeString(sep);var pos=!sep?-1:str.indexOf(sep);return~pos?str.slice(pos+sep.length,str.length):str}},{"./helper/makeString":20}],55:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function strRightBack(str,sep){str=makeString(str);sep=makeString(sep);var pos=!sep?-1:str.lastIndexOf(sep);return~pos?str.slice(pos+sep.length,str.length):str}},{"./helper/makeString":20}],56:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function stripTags(str){return makeString(str).replace(/<\/?[^>]+>/g,"")}},{"./helper/makeString":20}],57:[function(require,module,exports){var adjacent=require("./helper/adjacent");module.exports=function succ(str){return adjacent(str,1)}},{"./helper/adjacent":15}],58:[function(require,module,exports){module.exports=function surround(str,wrapper){return[wrapper,str,wrapper].join("")}},{}],59:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function swapCase(str){return makeString(str).replace(/\S/g,function(c){return c===c.toUpperCase()?c.toLowerCase():c.toUpperCase()})}},{"./helper/makeString":20}],60:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function titleize(str){return makeString(str).toLowerCase().replace(/(?:^|\s|-)\S/g,function(c){return c.toUpperCase()})}},{"./helper/makeString":20}],61:[function(require,module,exports){var trim=require("./trim");function boolMatch(s,matchers){var i,matcher,down=s.toLowerCase();matchers=[].concat(matchers);for(i=0;i<matchers.length;i+=1){matcher=matchers[i];if(!matcher)continue;if(matcher.test&&matcher.test(s))return true;if(matcher.toLowerCase()===down)return true}}module.exports=function toBoolean(str,trueValues,falseValues){if(typeof str==="number")str=""+str;if(typeof str!=="string")return!!str;str=trim(str);if(boolMatch(str,trueValues||["true","1"]))return true;if(boolMatch(str,falseValues||["false","0"]))return false}},{"./trim":65}],62:[function(require,module,exports){module.exports=function toNumber(num,precision){if(num==null)return 0;var factor=Math.pow(10,isFinite(precision)?precision:0);return Math.round(num*factor)/factor}},{}],63:[function(require,module,exports){var rtrim=require("./rtrim");module.exports=function toSentence(array,separator,lastSeparator,serial){separator=separator||", ";lastSeparator=lastSeparator||" and ";var a=array.slice(),lastMember=a.pop();if(array.length>2&&serial)lastSeparator=rtrim(separator)+lastSeparator;return a.length?a.join(separator)+lastSeparator+lastMember:lastMember}},{"./rtrim":47}],64:[function(require,module,exports){var toSentence=require("./toSentence");module.exports=function toSentenceSerial(array,sep,lastSep){return toSentence(array,sep,lastSep,true)}},{"./toSentence":63}],65:[function(require,module,exports){var makeString=require("./helper/makeString");var defaultToWhiteSpace=require("./helper/defaultToWhiteSpace");var nativeTrim=String.prototype.trim;module.exports=function trim(str,characters){str=makeString(str);if(!characters&&nativeTrim)return nativeTrim.call(str);characters=defaultToWhiteSpace(characters);return str.replace(new RegExp("^"+characters+"+|"+characters+"+$","g"),"")}},{"./helper/defaultToWhiteSpace":16,"./helper/makeString":20}],66:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function truncate(str,length,truncateStr){str=makeString(str);truncateStr=truncateStr||"...";length=~~length;return str.length>length?str.slice(0,length)+truncateStr:str}},{"./helper/makeString":20}],67:[function(require,module,exports){var trim=require("./trim");module.exports=function underscored(str){return trim(str).replace(/([a-z\d])([A-Z]+)/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase()}},{"./trim":65}],68:[function(require,module,exports){var makeString=require("./helper/makeString");var htmlEntities=require("./helper/htmlEntities");module.exports=function unescapeHTML(str){return makeString(str).replace(/\&([^;]{1,10});/g,function(entity,entityCode){var match;if(entityCode in htmlEntities){return htmlEntities[entityCode]}else if(match=entityCode.match(/^#x([\da-fA-F]+)$/)){return String.fromCharCode(parseInt(match[1],16))}else if(match=entityCode.match(/^#(\d+)$/)){return String.fromCharCode(~~match[1])}else{return entity}})}},{"./helper/htmlEntities":19,"./helper/makeString":20}],69:[function(require,module,exports){module.exports=function unquote(str,quoteChar){quoteChar=quoteChar||'"';if(str[0]===quoteChar&&str[str.length-1]===quoteChar)return str.slice(1,str.length-1);else return str}},{}],70:[function(require,module,exports){var deprecate=require("util-deprecate");module.exports=deprecate(require("sprintf-js").vsprintf,"vsprintf() will be removed in the next major release, use the sprintf-js package instead.")},{"sprintf-js":36,"util-deprecate":37}],71:[function(require,module,exports){var isBlank=require("./isBlank");var trim=require("./trim");module.exports=function words(str,delimiter){if(isBlank(str))return[];return trim(str,delimiter).split(delimiter||/\s+/)}},{"./isBlank":27,"./trim":65}],72:[function(require,module,exports){var makeString=require("./helper/makeString");module.exports=function wrap(str,options){str=makeString(str);options=options||{};var width=options.width||75;var seperator=options.seperator||"\n";var cut=options.cut||false;var preserveSpaces=options.preserveSpaces||false;var trailingSpaces=options.trailingSpaces||false;var result;if(width<=0){return str}else if(!cut){var words=str.split(" ");var current_column=0;result="";while(words.length>0){if(1+words[0].length+current_column>width){if(current_column>0){if(preserveSpaces){result+=" ";current_column++}else if(trailingSpaces){while(current_column<width){result+=" ";current_column++}}result+=seperator;current_column=0}}if(current_column>0){result+=" ";current_column++}result+=words[0];current_column+=words[0].length;words.shift()}if(trailingSpaces){while(current_column<width){result+=" ";current_column++}}return result}else{var index=0;result="";while(index<str.length){if(index%width==0&&index>0){result+=seperator}result+=str.charAt(index);index++}if(trailingSpaces){while(index%width>0){result+=" ";index++}}return result}}},{"./helper/makeString":20}]},{},[25])(25)});