<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * CLI script to refresh My Subscription menu items
 *
 * This script can be run manually by administrators to refresh the
 * My Subscription menu items in case they become corrupted or need
 * to be updated.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require(__DIR__ . '/../../../config.php');
require_once($CFG->libdir . '/clilib.php');
require_once(__DIR__ . '/../classes/helper/menu_helper.php');

use enrol_stripepaymentpro\helper\menu_helper;

// Get CLI options.
list($options, $unrecognized) = cli_get_params(
    [
        'help' => false,
        'status' => false,
        'force' => false,
    ],
    [
        'h' => 'help',
        's' => 'status',
        'f' => 'force',
    ]
);

if ($unrecognized) {
    $unrecognized = implode("\n  ", $unrecognized);
    cli_error(get_string('cliunknowoption', 'admin', $unrecognized));
}

if ($options['help']) {
    $help = "Refresh My Subscription menu items for StripePaymentPro plugin.

Options:
-h, --help          Print out this help
-s, --status        Show current menu status without making changes
-f, --force         Force refresh menu items (clears caches)

Examples:
\$ php refresh_menu.php --status
\$ php refresh_menu.php
\$ php refresh_menu.php --force
";

    echo $help;
    exit(0);
}

// Show current status if requested.
if ($options['status']) {
    cli_heading('Current Menu Status');
    
    $status = menu_helper::get_menu_status();
    
    echo "License Status: " . ($status['licensestatus'] ?: 'Not set') . "\n";
    echo "Menu Item Exists: " . ($status['menu_item_exists'] ? 'Yes' : 'No') . "\n";
    echo "Expected Menu Item: " . $status['expected_menu_item'] . "\n";
    echo "\nCurrent User Menu Items:\n";
    
    if (empty($status['current_user_menu_items'])) {
        echo "  (No custom user menu items found)\n";
    } else {
        foreach ($status['current_user_menu_items'] as $item) {
            $marker = ($item === $status['expected_menu_item']) ? ' ← (Expected item)' : '';
            echo "  - " . $item . $marker . "\n";
        }
    }
    
    exit(0);
}

// Refresh menu items.
cli_heading('Refreshing My Subscription Menu Items');

try {
    if ($options['force']) {
        echo "Force refreshing menu items (with cache clearing)...\n";
        menu_helper::force_refresh_menu_items();
    } else {
        echo "Refreshing menu items...\n";
        menu_helper::refreshsubscriptionmenuitems();
    }
    
    // Show updated status.
    echo "\nRefresh completed. New status:\n";
    $status = menu_helper::get_menu_status();
    
    echo "License Status: " . ($status['licensestatus'] ?: 'Not set') . "\n";
    echo "Menu Item Exists: " . ($status['menu_item_exists'] ? 'Yes' : 'No') . "\n";
    
    if ($status['licensestatus'] === 'active' && $status['menu_item_exists']) {
        echo "\n✓ Menu item successfully added for active license.\n";
    } else if ($status['licensestatus'] !== 'active' && !$status['menu_item_exists']) {
        echo "\n✓ Menu item successfully removed for inactive license.\n";
    } else {
        echo "\n⚠ Warning: Menu state may not match license status.\n";
    }
    
} catch (Exception $e) {
    cli_error("Error refreshing menu items: " . $e->getMessage());
}

exit(0);
