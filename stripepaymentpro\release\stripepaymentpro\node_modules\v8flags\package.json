{"name": "v8flags", "version": "3.2.0", "description": "Get available v8 and Node.js flags.", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/v8flags", "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "files": ["index.js", "config-path.js", "LICENSE"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {"homedir-polyfill": "^1.0.1"}, "devDependencies": {"async": "^2.5.0", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "proxyquire": "^1.8.0"}, "keywords": ["v8 flags", "harmony flags"]}