<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon creation form for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\form;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->libdir.'/formslib.php');

/**
 * Coupon creation form class
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class coupon_form extends \moodleform {

    /**
     * Add elements to form
     */
    public function definition() {
        $mform = $this->_form;
        list($stripecourselist, $plugin, $plugincore) = $this->_customdata;

        // Coupon Name
        $mform->addElement('text', 'couponname', get_string('couponname', 'enrol_stripepaymentpro'));
        $mform->setType('couponname', PARAM_RAW);
        $mform->addRule('couponname', get_string('couponnamerequired', 'enrol_stripepaymentpro'), 'required', null);
        $mform->setDefault('couponname', '');

        // Coupon Type
        $coupontypes = [
            'amountoff' => get_string('discounttypefixed', 'enrol_stripepaymentpro'),
            'percentoff' => get_string('discounttypepercentage', 'enrol_stripepaymentpro')
        ];

        $mform->addElement('select', 'coupontypes', get_string('coupontypes', 'enrol_stripepaymentpro'), $coupontypes);
        $mform->setDefault('coupontypes', get_config('enrol_stripepaymentpro', 'coupontypes'));

        // Coupon amount
        $mform->addElement('text', 'discountamount', get_string('discountamount', 'enrol_stripepaymentpro'));
        $mform->setType('discountamount', PARAM_FLOAT);
        $mform->addRule('discountamount', get_string('discountamountnonzero', 'enrol_stripepaymentpro'), 'nonzero', null);
        $mform->setDefault('discountamount', 0);

        // Coupon currency
        $couponcurrencies = $plugincore->get_currencies();
        $mform->addElement('select', 'coupon_currency', get_string('couponcurrency', 'enrol_stripepaymentpro'), $couponcurrencies);
        $mform->setDefault('coupon_currency', get_config('enrol_stripepaymentpro', 'coupon_currency'));

        // Coupon Duration
        $coupondurations = [
            'forever' => get_string('coupondurationforever', 'enrol_stripepaymentpro'),
            'once' => get_string('coupondurationonce', 'enrol_stripepaymentpro'),
            'repeating' => get_string('coupondurationmultiplemonths', 'enrol_stripepaymentpro'),
        ];

        $mform->addElement('select', 'coupon_duration', get_string('couponduration', 'enrol_stripepaymentpro'), $coupondurations);
        $mform->setDefault('coupon_duration', get_config('enrol_stripepaymentpro', 'coupon_duration'));

        $mform->addElement('text', 'coupon_duration_multiple_months_val',
            get_string('coupondurationmultiplemonthsval', 'enrol_stripepaymentpro'), ['size' => 2]);
        $mform->setType('coupon_duration_multiple_months_val', PARAM_INT);
        $mform->setDefault('coupon_duration_multiple_months_val', 1);
        $mform->addRule('coupon_duration_multiple_months_val',
            get_string('coupondurationmultiplemonthsvalnonzero', 'enrol_stripepaymentpro'), 'nonzero', null);
        $mform->hideIf('coupon_duration_multiple_months_val', 'coupon_duration', 'ne', 'repeating');

        // Coupon expiry date
        $mform->addElement('date_time_selector', 'coupon_expiry',
            get_string('couponexpiry', 'enrol_stripepaymentpro'), ['optional' => true]);
        $mform->setDefault('coupon_expiry', 0);

        // Coupon course assignment
        $stripecourselist['0'] = get_string('couponforallcourses', 'enrol_stripepaymentpro');
        $mform->addElement('select', 'coupon_course_assignment',
            get_string('couponcourseassignment', 'enrol_stripepaymentpro'), $stripecourselist);
        $mform->setDefault('coupon_course_assignment', 0);

        // Action buttons
        $this->add_action_buttons();
    }

    /**
     * Custom validation
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    function validation($data, $files) {
        $errors = parent::validation($data, $files);

        if ($data["couponname"] == "") {
            $errors['couponname'] = get_string('couponnamerequired', 'enrol_stripepaymentpro');
        }

        if ($data["coupon_expiry"] > 0 && $data["coupon_expiry"] <= time()) {
            $errors['coupon_expiry'] = get_string('invalidcouponexpiry', 'enrol_stripepaymentpro');
        }

        if ($data["discountamount"] <= 0) {
            $errors['discountamount'] = get_string('invalidcouponamount', 'enrol_stripepaymentpro');
        } else if ($data["discountamount"] > 100 && $data["coupontypes"] == "percent_off") {
            $errors['discountamount'] = get_string('invaliddiscountpercentage', 'enrol_stripepaymentpro');
        }

        return $errors;
    }
}
