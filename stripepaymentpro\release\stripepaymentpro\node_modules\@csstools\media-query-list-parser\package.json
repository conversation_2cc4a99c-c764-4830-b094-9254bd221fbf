{"name": "@csstools/media-query-list-parser", "description": "Parse CSS media query lists.", "version": "2.1.13", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": "^14 || ^16 || >=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"@csstools/css-parser-algorithms": "^2.7.1", "@csstools/css-tokenizer": "^2.4.1"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/media-query-list-parser#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/media-query-list-parser"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "media query", "parser"]}