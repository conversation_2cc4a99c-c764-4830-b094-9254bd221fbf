define(["exports","core/ajax","core/str"],function(e,t,n){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=o(t),r=o(n);let s={};const l=()=>{const e=document.getElementById("couponsearch"),t=document.getElementById("clear-search"),n=document.getElementById("coupons-table");if(!e||!n)return;const o=()=>{const t=e.value.toLowerCase().trim(),o=document.getElementById("course-filter-select"),r=o?o.value:"All course",s=n.querySelectorAll("tbody tr.coupon-row");let l=0;s.forEach(e=>{const n=e.getAttribute("datacoursename")?.toLowerCase()||"",o=e.getAttribute("datacouponname")?.toLowerCase()||"",c=e.getAttribute("datadiscounttext")?.toLowerCase()||"",s=!t||n.includes(t)||o.includes(t)||c.includes(t),a="All course"===r||e.getAttribute("datacoursename")===r,i=s&&a;e.style.display=i?"":"none",i&&l++}),c(l,t)},c=(e,t)=>{let o=n.querySelector(".noresultsrow");0===e&&""!==t?(o||(o=document.createElement("tr"),o.className="noresultsrow",o.innerHTML=`<td colspan="4" class="text-center text-muted py-4">\n                    <i class="fa fa-search mr-2"></i>${s.noResults}\n                </td>`,n.querySelector("tbody").appendChild(o)),o.style.display=""):o&&(o.style.display="none")},r=()=>{e.value="",o(),e.focus()};e.addEventListener("input",o),e.addEventListener("keyup",e=>{"Escape"===e.key&&r()}),t&&t.addEventListener("click",r)},a=()=>{document.addEventListener("click",e=>{const t=e.target.closest(".copycouponname");if(!t)return;const n=t.getAttribute("datacouponname");if(!n)return;const o=document.createElement("textarea");o.value=n,document.body.appendChild(o),o.select(),o.setSelectionRange(0,99999);const c=t.innerHTML;try{document.execCommand("copy"),t.innerHTML=`<i class="fa fa-check ml-1" style="font-size: 0.8em; color: green;"></i> ${s.copied}`,t.style.color="green"}catch(e){t.innerHTML=`<i class="fa fa-times ml-1" style="font-size: 0.8em; color: red;"></i> ${s.failed}`,t.style.color="red"}finally{setTimeout(()=>{t.innerHTML=c,t.style.color=""},2e3),document.body.removeChild(o)}})},i=async(e,t,n)=>{if(confirm(s.confirmDelete))try{const o=n.innerHTML;n.innerHTML='<i class="fa fa-spinner fa-spin"></i>',n.disabled=!0;const r={methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:e,couponid:t}},l=await c.default.call([r])[0];l.success?n.closest("tr")?.remove():(n.innerHTML=o,n.disabled=!1,alert(`${s.errorDelete}: ${l.message||"Unknown error"}`))}catch(e){n.innerHTML='<i class="fa fa-trash"></i>',n.disabled=!1,alert(`${s.errorDelete}: ${e.message}`)}},d=async(e,t)=>{if(confirm(s.confirmDeleteAll))try{const n=t.innerHTML;t.innerHTML='<i class="fa fa-spinner fa-spin"></i>',t.disabled=!0;const o={methodname:"moodle_stripepaymentpro_deactivate_all_coupons",args:{courseid:e}},r=await c.default.call([o])[0];r.success?(alert(`${s.success}: ${r.message}`),window.location.reload()):(t.innerHTML=n,t.disabled=!1,alert(`${s.errorDeleteAll}: ${r.message||"Unknown error"}`))}catch(e){t.innerHTML='<i class="fa fa-trash-alt"></i>',t.disabled=!1,alert(`${s.errorDeleteAll}: ${e.message}`)}},u=()=>{const e=document.getElementById("bulk-action-select"),t=document.getElementById("apply-bulk-action"),n=document.querySelector('thead input[type="checkbox"]');e&&t&&(n&&n.addEventListener("change",function(){document.querySelectorAll('tbody input[type="checkbox"]').forEach(e=>{e.checked=this.checked})}),document.addEventListener("change",function(e){if("checkbox"===e.target.type&&e.target.closest("tbody")){const e=document.querySelectorAll('tbody input[type="checkbox"]'),t=document.querySelectorAll('tbody input[type="checkbox"]:checked');n&&(n.checked=t.length===e.length&&e.length>0,n.indeterminate=t.length>0&&t.length<e.length)}}),t.addEventListener("click",async function(t){t.preventDefault();const o=e.value,r=document.querySelectorAll('tbody input[type="checkbox"]:checked');if("1"===o&&r.length>0){if(!confirm(`Are you sure you want to delete ${r.length} selected coupon(s)?`))return;const e=this.innerHTML;this.innerHTML='<i class="fa fa-spinner fa-spin"></i> Processing...',this.disabled=!0;try{const e=Array.from(r).map(e=>{const t=e.closest("tr"),n=t.getAttribute("data-course-id"),o=t.querySelector(".deactivate-coupon-btn").getAttribute("data-coupon-id");return c.default.call([{methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:n,couponid:o}}])[0]}),t=await Promise.all(e);let o=0,s=0;t.forEach((e,t)=>{e.success?(r[t].closest("tr").remove(),o++):s++}),o>0&&alert(`Successfully deleted ${o} coupon(s).`),s>0&&alert(`Failed to delete ${s} coupon(s).`),n&&(n.checked=!1,n.indeterminate=!1)}catch(e){alert(`Error during bulk delete: ${e.message}`)}finally{this.innerHTML=e,this.disabled=!1}}}))},p=()=>{const e=document.getElementById("course-filter-select"),t=document.getElementById("coupons-table");if(!e||!t)return;const n=new Set,o=t.querySelectorAll("tbody tr.coupon-row");for(o.forEach(e=>{const t=e.getAttribute("datacoursename");t&&n.add(t)});e.children.length>1;)e.removeChild(e.lastChild);n.forEach(t=>{const n=document.createElement("option");n.value=t,n.textContent=t,e.appendChild(n)}),e.addEventListener("change",function(){const e=this.value;o.forEach(t=>{const n=t.getAttribute("datacoursename"),o="All course"===e||n===e;t.style.display=o?"":"none"});const t=document.getElementById("couponsearch");t&&t.value.trim()&&t.dispatchEvent(new Event("input"))})};window.handleCouponDelete=i,window.handleDeleteAllCoupons=d,e.handleCouponDelete=i,e.handleDeleteAllCoupons=d,e.init=()=>{r.default.get_strings([{key:"nosearchresults",component:"enrol_stripepaymentpro"},{key:"copied",component:"enrol_stripepaymentpro"},{key:"failed",component:"enrol_stripepaymentpro"},{key:"areyousuredeletecoupon",component:"enrol_stripepaymentpro"},{key:"areyousuredeleteallcoupons",component:"enrol_stripepaymentpro"},{key:"errordeletingcoupon",component:"enrol_stripepaymentpro"},{key:"successmessage",component:"enrol_stripepaymentpro"}]).then(e=>{s={noResults:e[0],copied:e[1],failed:e[2],confirmDelete:e[3],confirmDeleteAll:e[4],errorDelete:e[5],errorDeleteAll:e[6],success:e[7]};const t=document.getElementById("allcouponbutton"),n=document.getElementById("generatecouponsbutton"),o=document.getElementById("allcouponssection"),c=document.getElementById("generatecouponsection"),r=()=>{o&&c&&(o.style.display="block",c.style.display="none"),t&&(t.classList.remove("btnsecondary"),t.classList.add("btnprimary","active")),n&&(n.classList.remove("btnprimary","active"),n.classList.add("btnsecondary"))},i=()=>{o&&c&&(o.style.display="none",c.style.display="block"),n&&(n.classList.remove("btnsecondary"),n.classList.add("btnprimary","active")),t&&(t.classList.remove("btnprimary","active"),t.classList.add("btnsecondary"))};t&&t.addEventListener("click",r),n&&n.addEventListener("click",i),r(),l(),a(),u(),p()}).catch(e=>{console.error("Failed to load localized strings:",e)})},Object.defineProperty(e,"__esModule",{value:!0})});
