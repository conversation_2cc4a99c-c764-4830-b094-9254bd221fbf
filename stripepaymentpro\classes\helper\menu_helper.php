<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Menu helper for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\helper;

defined('MOODLE_INTERNAL') || die();

/**
 * Helper class for managing custom menu items
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class menu_helper {

    /** @var string The subscription menu item for main menu */
    const subscriptionmenuitem = 'My Subscription|/enrol/stripepaymentpro/mysubscription.php';
    
    /** @var string The subscription menu item for user menu */
    const usersubscriptionmenuitem = 'My Subscriptions|/enrol/stripepaymentpro/mysubscription.php';

    /**
     * Refresh subscription menu items based on license status
     *
     * @return void
     */
    public static function refreshsubscriptionmenuitems() {
        self::cleanoldmenuitems();
        
        $licensestatus = get_config('enrol_stripepaymentpro', 'licensestatus');
        
        if ($licensestatus === 'active') {
            self::addsubscriptionmenuitem();
        } else {
            self::removesubscriptionmenuitem();
        }
    }

    /**
     * Clean up old menu items that might exist with different URLs
     *
     * @return void
     */
    private static function cleanoldmenuitems() {
        global $CFG;
        
        // Old menu items that might exist
        $oldmenuitems = [
            'My Subscription|/enrol/stripepaymentpro/mysubscription.php',
        ];
        
        // Clean main menu
        $currentcustommenuItems = str_replace(["\r\n", "\r"], "\n", $CFG->custommenuitems);
        $lines = explode("\n", $currentcustommenuItems);
        $lines = array_map('trim', $lines);
        $lines = array_filter($lines); // Remove empty lines
        
        foreach ($oldmenuitems as $olditem) {
            $lines = array_diff($lines, [$olditem]);
        }
        
        set_config('custommenuitems', implode("\n", $lines));
        
        // Clean user menu
        $currentcustomusermenuItems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
        $userlines = explode("\n", $currentcustomusermenuItems);
        $userlines = array_map('trim', $userlines);
        $userlines = array_filter($userlines); // Remove empty lines
        
        foreach ($oldmenuitems as $olditem) {
            $userlines = array_diff($userlines, [$olditem]);
        }
        
        set_config('customusermenuitems', implode("\n", $userlines));
    }

    /**
     * Add subscription menu item to user menu
     *
     * @return void
     */
    private static function addsubscriptionmenuitem() {
        global $CFG;
        
        $currentcustomusermenuItems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
        $userlines = explode("\n", $currentcustomusermenuItems);
        $userlines = array_map('trim', $userlines);
        $userlines = array_filter($userlines); // Remove empty lines
        
        // Add the menu item if it doesn't exist
        if (!in_array(self::usersubscriptionmenuitem, $userlines)) {
            // Insert after the first item (usually "Dashboard" or "Profile")
            if (count($userlines) > 0) {
                array_splice($userlines, 1, 0, [self::usersubscriptionmenuitem]);
            } else {
                $userlines[] = self::usersubscriptionmenuitem;
            }
            
            set_config('customusermenuitems', implode("\n", $userlines));
        }
    }

    /**
     * Remove subscription menu item from user menu
     *
     * @return void
     */
    private static function removesubscriptionmenuitem() {
        global $CFG;
        
        $currentcustomusermenuItems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
        $userlines = explode("\n", $currentcustomusermenuItems);
        $userlines = array_map('trim', $userlines);
        $userlines = array_filter($userlines); // Remove empty lines
        
        // Remove the menu item if it exists
        if (in_array(self::usersubscriptionmenuitem, $userlines)) {
            $userlines = array_diff($userlines, [self::usersubscriptionmenuitem]);
            set_config('customusermenuitems', implode("\n", $userlines));
        }
    }

    /**
     * Force refresh menu items (useful for upgrade scripts)
     *
     * @return void
     */
    public static function force_refresh_menu_items() {
        // Clear any cached menu data
        purge_all_caches();
        
        // Refresh the menu items
        self::refreshsubscriptionmenuitems();
        
        // Clear caches again to ensure changes take effect
        purge_all_caches();
    }

    /**
     * Get current menu status for debugging
     *
     * @return array Status information
     */
    public static function get_menu_status() {
        global $CFG;
        
        $licensestatus = get_config('enrol_stripepaymentpro', 'licensestatus');
        
        $currentcustomusermenuItems = str_replace(["\r\n", "\r"], "\n", $CFG->customusermenuitems);
        $userlines = explode("\n", $currentcustomusermenuItems);
        $userlines = array_map('trim', $userlines);
        $userlines = array_filter($userlines);
        
        return [
            'licensestatus' => $licensestatus,
            'menu_item_exists' => in_array(self::usersubscriptionmenuitem, $userlines),
            'current_user_menu_items' => $userlines,
            'expected_menu_item' => self::usersubscriptionmenuitem
        ];
    }
}
