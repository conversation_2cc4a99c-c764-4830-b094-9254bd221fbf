<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="ceaafdf0-f5cb-4dca-a919-6bfe6e1f5913" name="Default" comment="" />
    <ignored path="tiny-lr.iws" />
    <ignored path=".idea/workspace.xml" />
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="default_target" />
  <component name="FavoritesManager">
    <favorites_list name="tiny-lr" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="package.json" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="120">
              <caret line="8" column="14" selection-start-line="8" selection-start-column="14" selection-end-line="8" selection-end-column="14" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="Git.Settings">
    <option name="UPDATE_TYPE" value="REBASE" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="npm-version-amaze" />
      </map>
    </option>
    <option name="FORCE_PUSH_ALLOWED" value="true" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/package.json" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="ProjectFrameBounds">
    <option name="y" value="23" />
    <option name="width" value="1440" />
    <option name="height" value="873" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="false">
    <OptionsSetting value="true" id="Add" />
    <OptionsSetting value="true" id="Remove" />
    <OptionsSetting value="true" id="Checkout" />
    <OptionsSetting value="false" id="Update" />
    <OptionsSetting value="true" id="Status" />
    <OptionsSetting value="true" id="Edit" />
    <ConfirmationsSetting value="0" id="Add" />
    <ConfirmationsSetting value="0" id="Remove" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="tiny-lr" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="tiny-lr" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="tiny-lr" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
      <pane id="Scratches" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="fileTemplates" />
    <property name="JavaScriptPreferStrict" value="false" />
    <property name="JavaScriptWeakerCompletionTypeGuess" value="true" />
    <property name="js.eslint.nodeInterpreter" value="/usr/local/bin/node" />
    <property name="js.eslint.eslintPackage" value="/usr/local/lib/node_modules/eslint" />
    <property name="js-jscs-nodeInterpreter" value="/usr/local/bin/node" />
    <property name="settings.editor.splitter.proportion" value="0.2" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="javascript.nodejs.core.library.configured.version" value="4.2.3" />
    <property name="nodejs_interpreter_path" value="/usr/local/bin/node" />
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="GO_FMT" value="false" />
  </component>
  <component name="RestoreUpdateTree" date="Moments ago" ActionInfo="_Update">
    <UpdatedFiles>
      <FILE-GROUP>
        <option name="myUpdateName" value="Updated from server" />
        <option name="myStatusName" value="Changed on server" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="CHANGED_ON_SERVER" />
        <FILE-GROUP>
          <option name="myUpdateName" value="Updated" />
          <option name="myStatusName" value="Changed" />
          <option name="mySupportsDeletion" value="false" />
          <option name="myCanBeAbsent" value="false" />
          <option name="myId" value="UPDATED" />
          <PATH vcs="Git" revision="">$PROJECT_DIR$/package.json</PATH>
        </FILE-GROUP>
        <FILE-GROUP>
          <option name="myUpdateName" value="Created" />
          <option name="myStatusName" value="Created" />
          <option name="mySupportsDeletion" value="false" />
          <option name="myCanBeAbsent" value="false" />
          <option name="myId" value="CREATED" />
        </FILE-GROUP>
        <FILE-GROUP>
          <option name="myUpdateName" value="Deleted" />
          <option name="myStatusName" value="Deleted" />
          <option name="mySupportsDeletion" value="false" />
          <option name="myCanBeAbsent" value="true" />
          <option name="myId" value="REMOVED_FROM_REPOSITORY" />
        </FILE-GROUP>
        <FILE-GROUP>
          <option name="myUpdateName" value="Restored" />
          <option name="myStatusName" value="Will be restored" />
          <option name="mySupportsDeletion" value="false" />
          <option name="myCanBeAbsent" value="false" />
          <option name="myId" value="RESTORED" />
        </FILE-GROUP>
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Modified" />
        <option name="myStatusName" value="Modified" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="MODIFIED" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Skipped" />
        <option name="myStatusName" value="Skipped" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="SKIPPED" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Merged with conflicts" />
        <option name="myStatusName" value="Will be merged with conflicts" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="MERGED_WITH_CONFLICTS" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Merged with tree conflicts" />
        <option name="myStatusName" value="Merged with tree conflicts" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="MERGED_WITH_TREE_CONFLICT" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Merged with property conflicts" />
        <option name="myStatusName" value="Will be merged with property conflicts" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="MERGED_WITH_PROPERTY_CONFLICT" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Merged" />
        <option name="myStatusName" value="Will be merged" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="MERGED" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Not in repository" />
        <option name="myStatusName" value="Not in repository" />
        <option name="mySupportsDeletion" value="true" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="UNKNOWN" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Locally added" />
        <option name="myStatusName" value="Locally added" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="LOCALLY_ADDED" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Locally removed" />
        <option name="myStatusName" value="Locally removed" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="LOCALLY_REMOVED" />
      </FILE-GROUP>
      <FILE-GROUP>
        <option name="myUpdateName" value="Switched" />
        <option name="myStatusName" value="Switched" />
        <option name="mySupportsDeletion" value="false" />
        <option name="myCanBeAbsent" value="false" />
        <option name="myId" value="SWITCHED" />
      </FILE-GROUP>
    </UpdatedFiles>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="23" width="1440" height="873" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.24947146" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="true" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32980132" sideWeight="0.5" order="-1" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="true" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Make `npm version patch` do all the work&#10;&#10;&lt;3 @rwjblue @nathanhammond" />
    <option name="LAST_COMMIT_MESSAGE" value="Make `npm version patch` do all the work&#10;&#10;&lt;3 @rwjblue @nathanhammond" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="120">
          <caret line="8" column="14" selection-start-line="8" selection-start-column="14" selection-end-line="8" selection-end-column="14" />
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>