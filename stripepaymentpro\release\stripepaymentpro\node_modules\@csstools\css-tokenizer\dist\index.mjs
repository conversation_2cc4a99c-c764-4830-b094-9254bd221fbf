class ParseError extends Error{sourceStart;sourceEnd;parserState;constructor(e,n,o,t){super(e),this.name="ParseError",this.sourceStart=n,this.sourceEnd=o,this.parserState=t}}class ParseErrorWithToken extends ParseError{token;constructor(e,n,o,t,r){super(e,n,o,t),this.token=r}}const e={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};class Reader{cursor=0;source="";codePointSource=[];representationIndices=[-1];length=0;representationStart=0;representationEnd=-1;constructor(e){this.source=e;{let n=-1,o="";for(o of e)n+=o.length,this.codePointSource.push(o.codePointAt(0)),this.representationIndices.push(n)}this.length=this.codePointSource.length}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.representationIndices[this.cursor]}readCodePoint(e=1){const n=this.codePointSource[this.cursor];return void 0!==n&&(this.cursor=this.cursor+e,this.representationEnd=this.representationIndices[this.cursor],n)}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.representationIndices[this.cursor]}resetRepresentation(){this.representationStart=this.representationIndices[this.cursor]+1,this.representationEnd=-1}}const n="undefined"!=typeof globalThis&&"structuredClone"in globalThis;function cloneTokens(e){return n?structuredClone(e):JSON.parse(JSON.stringify(e))}function stringify(...e){let n="";for(let o=0;o<e.length;o++)n+=e[o][1];return n}const o=39,t=42,r=8,i=13,s=9,c=58,a=44,u=64,d=127,p=33,P=12,S=46,C=62,l=45,f=31,m=69,h=101,k=123,E=40,T=91,v=60,g=10,I=11,U=95,O=1114111,D=0,R=35,w=37,L=43,A=34,x=65533,W=92,y=125,F=41,q=93,N=59,b=14,B=47,H=32,V=117,K=85,z=114,M=82,$=108,J=76,_=63,j=48,Q=70;function checkIfFourCodePointsWouldStartCDO(e){return e.codePointSource[e.cursor]===v&&e.codePointSource[e.cursor+1]===p&&e.codePointSource[e.cursor+2]===l&&e.codePointSource[e.cursor+3]===l}function isDigitCodePoint(e){return e>=48&&e<=57}function isUppercaseLetterCodePoint(e){return e>=65&&e<=90}function isLowercaseLetterCodePoint(e){return e>=97&&e<=122}function isHexDigitCodePoint(e){return isDigitCodePoint(e)||e>=97&&e<=102||e>=65&&e<=70}function isLetterCodePoint(e){return isLowercaseLetterCodePoint(e)||isUppercaseLetterCodePoint(e)}function isIdentStartCodePoint(e){return isLetterCodePoint(e)||isNonASCII_IdentCodePoint(e)||e===U}function isIdentCodePoint(e){return isIdentStartCodePoint(e)||isDigitCodePoint(e)||e===l}function isNonASCII_IdentCodePoint(e){return 183===e||8204===e||8205===e||8255===e||8256===e||8204===e||(192<=e&&e<=214||216<=e&&e<=246||248<=e&&e<=893||895<=e&&e<=8191||8304<=e&&e<=8591||11264<=e&&e<=12271||12289<=e&&e<=55295||63744<=e&&e<=64975||65008<=e&&e<=65533||e>=65536)}function isNewLine(e){return e===g||e===i||e===P}function isWhitespace(e){return e===H||e===g||e===s||e===i||e===P}function checkIfTwoCodePointsAreAValidEscape(e){return e.codePointSource[e.cursor]===W&&!isNewLine(e.codePointSource[e.cursor+1])}function checkIfThreeCodePointsWouldStartAnIdentSequence(e,n){return n.codePointSource[n.cursor]===l?n.codePointSource[n.cursor+1]===l||(!!isIdentStartCodePoint(n.codePointSource[n.cursor+1])||n.codePointSource[n.cursor+1]===W&&!isNewLine(n.codePointSource[n.cursor+2])):!!isIdentStartCodePoint(n.codePointSource[n.cursor])||checkIfTwoCodePointsAreAValidEscape(n)}function checkIfThreeCodePointsWouldStartANumber(e){return e.codePointSource[e.cursor]===L||e.codePointSource[e.cursor]===l?!!isDigitCodePoint(e.codePointSource[e.cursor+1])||e.codePointSource[e.cursor+1]===S&&isDigitCodePoint(e.codePointSource[e.cursor+2]):e.codePointSource[e.cursor]===S?isDigitCodePoint(e.codePointSource[e.cursor+1]):isDigitCodePoint(e.codePointSource[e.cursor])}function checkIfTwoCodePointsStartAComment(e){return e.codePointSource[e.cursor]===B&&e.codePointSource[e.cursor+1]===t}function checkIfThreeCodePointsWouldStartCDC(e){return e.codePointSource[e.cursor]===l&&e.codePointSource[e.cursor+1]===l&&e.codePointSource[e.cursor+2]===C}var G,X,Y;function mirrorVariantType(e){switch(e){case G.OpenParen:return G.CloseParen;case G.CloseParen:return G.OpenParen;case G.OpenCurly:return G.CloseCurly;case G.CloseCurly:return G.OpenCurly;case G.OpenSquare:return G.CloseSquare;case G.CloseSquare:return G.OpenSquare;default:return null}}function mirrorVariant(e){switch(e[0]){case G.OpenParen:return[G.CloseParen,")",-1,-1,void 0];case G.CloseParen:return[G.OpenParen,"(",-1,-1,void 0];case G.OpenCurly:return[G.CloseCurly,"}",-1,-1,void 0];case G.CloseCurly:return[G.OpenCurly,"{",-1,-1,void 0];case G.OpenSquare:return[G.CloseSquare,"]",-1,-1,void 0];case G.CloseSquare:return[G.OpenSquare,"[",-1,-1,void 0];default:return null}}function consumeComment(n,o){for(o.advanceCodePoint(2);;){const r=o.readCodePoint();if(!1===r){const t=[G.Comment,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInComment,o.representationStart,o.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],t)),t}if(r===t&&(void 0!==o.codePointSource[o.cursor]&&o.codePointSource[o.cursor]===B)){o.advanceCodePoint();break}}return[G.Comment,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,void 0]}function consumeEscapedCodePoint(n,o){const t=o.readCodePoint();if(!1===t)return n.onParseError(new ParseError(e.UnexpectedEOFInEscapedCodePoint,o.representationStart,o.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),x;if(isHexDigitCodePoint(t)){const e=[t];for(;void 0!==o.codePointSource[o.cursor]&&isHexDigitCodePoint(o.codePointSource[o.cursor])&&e.length<6;)e.push(o.codePointSource[o.cursor]),o.advanceCodePoint();isWhitespace(o.codePointSource[o.cursor])&&o.advanceCodePoint();const n=parseInt(String.fromCodePoint(...e),16);return 0===n?x:(r=n)>=55296&&r<=57343||n>O?x:n}var r;return t}function consumeIdentSequence(e,n){const o=[];for(;;)if(isIdentCodePoint(n.codePointSource[n.cursor]))o.push(n.codePointSource[n.cursor]),n.advanceCodePoint();else{if(!checkIfTwoCodePointsAreAValidEscape(n))return o;n.advanceCodePoint(),o.push(consumeEscapedCodePoint(e,n))}}function consumeHashToken(e,n){if(n.advanceCodePoint(),void 0!==n.codePointSource[n.cursor]&&(isIdentCodePoint(n.codePointSource[n.cursor])||checkIfTwoCodePointsAreAValidEscape(n))){let o=Y.Unrestricted;checkIfThreeCodePointsWouldStartAnIdentSequence(0,n)&&(o=Y.ID);const t=consumeIdentSequence(e,n);return[G.Hash,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t),type:o}]}return[G.Delim,"#",n.representationStart,n.representationEnd,{value:"#"}]}function consumeNumber(e,n){let o=X.Integer;for(n.codePointSource[n.cursor]!==L&&n.codePointSource[n.cursor]!==l||n.advanceCodePoint();isDigitCodePoint(n.codePointSource[n.cursor]);)n.advanceCodePoint();if(n.codePointSource[n.cursor]===S&&isDigitCodePoint(n.codePointSource[n.cursor+1]))for(n.advanceCodePoint(2),o=X.Number;isDigitCodePoint(n.codePointSource[n.cursor]);)n.advanceCodePoint();if(n.codePointSource[n.cursor]===h||n.codePointSource[n.cursor]===m){if(isDigitCodePoint(n.codePointSource[n.cursor+1]))n.advanceCodePoint(2);else{if(n.codePointSource[n.cursor+1]!==l&&n.codePointSource[n.cursor+1]!==L||!isDigitCodePoint(n.codePointSource[n.cursor+2]))return o;n.advanceCodePoint(3)}for(o=X.Number;isDigitCodePoint(n.codePointSource[n.cursor]);)n.advanceCodePoint()}return o}function consumeNumericToken(e,n){let o;{const e=n.codePointSource[n.cursor];e===l?o="-":e===L&&(o="+")}const t=consumeNumber(0,n),r=parseFloat(n.source.slice(n.representationStart,n.representationEnd+1));if(checkIfThreeCodePointsWouldStartAnIdentSequence(0,n)){const i=consumeIdentSequence(e,n);return[G.Dimension,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:r,signCharacter:o,type:t,unit:String.fromCodePoint(...i)}]}return n.codePointSource[n.cursor]===w?(n.advanceCodePoint(),[G.Percentage,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:r,signCharacter:o}]):[G.Number,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:r,signCharacter:o,type:t}]}function consumeWhiteSpace(e){for(;isWhitespace(e.codePointSource[e.cursor]);)e.advanceCodePoint();return[G.Whitespace,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function consumeStringToken(n,o){let t="";const r=o.readCodePoint();for(;;){const s=o.readCodePoint();if(!1===s){const r=[G.String,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:t}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInString,o.representationStart,o.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],r)),r}if(isNewLine(s)){o.unreadCodePoint();const t=[G.BadString,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedNewLineInString,o.representationStart,o.codePointSource[o.cursor]===i&&o.codePointSource[o.cursor+1]===g?o.representationEnd+2:o.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],t)),t}if(s===r)return[G.String,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:t}];if(s!==W)t+=String.fromCodePoint(s);else{if(void 0===o.codePointSource[o.cursor])continue;if(isNewLine(o.codePointSource[o.cursor])){o.codePointSource[o.cursor]===i&&o.codePointSource[o.cursor+1]===g&&o.advanceCodePoint(),o.advanceCodePoint();continue}t+=String.fromCodePoint(consumeEscapedCodePoint(n,o))}}}function checkIfCodePointsMatchURLIdent(e){return!(3!==e.length||e[0]!==V&&e[0]!==K||e[1]!==z&&e[1]!==M||e[2]!==$&&e[2]!==J)}function consumeBadURL(e,n){for(;;){if(void 0===n.codePointSource[n.cursor])return;if(n.codePointSource[n.cursor]===F)return void n.advanceCodePoint();checkIfTwoCodePointsAreAValidEscape(n)?(n.advanceCodePoint(),consumeEscapedCodePoint(e,n)):n.advanceCodePoint()}}function consumeUrlToken(n,t){for(;isWhitespace(t.codePointSource[t.cursor]);)t.advanceCodePoint();let i="";for(;;){if(void 0===t.codePointSource[t.cursor]){const o=[G.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:i}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],o)),o}if(t.codePointSource[t.cursor]===F)return t.advanceCodePoint(),[G.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:i}];if(isWhitespace(t.codePointSource[t.cursor])){for(t.advanceCodePoint();isWhitespace(t.codePointSource[t.cursor]);)t.advanceCodePoint();if(void 0===t.codePointSource[t.cursor]){const o=[G.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:i}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],o)),o}return t.codePointSource[t.cursor]===F?(t.advanceCodePoint(),[G.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:i}]):(consumeBadURL(n,t),[G.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0])}if(t.codePointSource[t.cursor]===A||t.codePointSource[t.cursor]===o||t.codePointSource[t.cursor]===E||((s=t.codePointSource[t.cursor])===I||s===d||D<=s&&s<=r||b<=s&&s<=f)){consumeBadURL(n,t);const o=[G.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedCharacterInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Unexpected U+0022 QUOTATION MARK (\"), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point"],o)),o}if(t.codePointSource[t.cursor]===W){if(checkIfTwoCodePointsAreAValidEscape(t)){t.advanceCodePoint(),i+=String.fromCodePoint(consumeEscapedCodePoint(n,t));continue}consumeBadURL(n,t);const o=[G.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],o)),o}i+=String.fromCodePoint(t.codePointSource[t.cursor]),t.advanceCodePoint()}var s}function consumeIdentLikeToken(e,n){const t=consumeIdentSequence(e,n);if(n.codePointSource[n.cursor]!==E)return[G.Ident,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}];if(checkIfCodePointsMatchURLIdent(t)){n.advanceCodePoint();let r=0;for(;;){const e=isWhitespace(n.codePointSource[n.cursor]),i=isWhitespace(n.codePointSource[n.cursor+1]);if(e&&i){r+=1,n.advanceCodePoint(1);continue}const s=e?n.codePointSource[n.cursor+1]:n.codePointSource[n.cursor];if(s===A||s===o)return r>0&&n.unreadCodePoint(r),[G.Function,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}];break}return consumeUrlToken(e,n)}return n.advanceCodePoint(),[G.Function,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}]}function checkIfThreeCodePointsWouldStartAUnicodeRange(e){return!(e.codePointSource[e.cursor]!==V&&e.codePointSource[e.cursor]!==K||e.codePointSource[e.cursor+1]!==L||e.codePointSource[e.cursor+2]!==_&&!isHexDigitCodePoint(e.codePointSource[e.cursor+2]))}function consumeUnicodeRangeToken(e,n){n.advanceCodePoint(2);const o=[],t=[];for(;void 0!==n.codePointSource[n.cursor]&&o.length<6&&isHexDigitCodePoint(n.codePointSource[n.cursor]);)o.push(n.codePointSource[n.cursor]),n.advanceCodePoint();for(;void 0!==n.codePointSource[n.cursor]&&o.length<6&&n.codePointSource[n.cursor]===_;)0===t.length&&t.push(...o),o.push(j),t.push(Q),n.advanceCodePoint();if(!t.length&&n.codePointSource[n.cursor]===l&&isHexDigitCodePoint(n.codePointSource[n.cursor+1]))for(n.advanceCodePoint();void 0!==n.codePointSource[n.cursor]&&t.length<6&&isHexDigitCodePoint(n.codePointSource[n.cursor]);)t.push(n.codePointSource[n.cursor]),n.advanceCodePoint();if(!t.length){const e=parseInt(String.fromCodePoint(...o),16);return[G.UnicodeRange,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{startOfRange:e,endOfRange:e}]}const r=parseInt(String.fromCodePoint(...o),16),i=parseInt(String.fromCodePoint(...t),16);return[G.UnicodeRange,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{startOfRange:r,endOfRange:i}]}function tokenize(e,n){const o=tokenizer(e,n),t=[];{for(;!o.endOfFile();){const e=o.nextToken();e&&t.push(e)}const e=o.nextToken();e&&t.push(e)}return t}function tokenizer(n,t){const r=n.css.valueOf(),d=n.unicodeRangesAllowed??!1,p=new Reader(r),C={onParseError:t?.onParseError??noop};return{nextToken:function nextToken(){p.resetRepresentation();const n=p.codePointSource[p.cursor];if(void 0===n)return[G.EOF,"",-1,-1,void 0];if(n===B&&checkIfTwoCodePointsStartAComment(p))return consumeComment(C,p);if(d&&(n===V||n===K)&&checkIfThreeCodePointsWouldStartAUnicodeRange(p))return consumeUnicodeRangeToken(0,p);if(isIdentStartCodePoint(n))return consumeIdentLikeToken(C,p);if(isDigitCodePoint(n))return consumeNumericToken(C,p);switch(n){case a:return p.advanceCodePoint(),[G.Comma,",",p.representationStart,p.representationEnd,void 0];case c:return p.advanceCodePoint(),[G.Colon,":",p.representationStart,p.representationEnd,void 0];case N:return p.advanceCodePoint(),[G.Semicolon,";",p.representationStart,p.representationEnd,void 0];case E:return p.advanceCodePoint(),[G.OpenParen,"(",p.representationStart,p.representationEnd,void 0];case F:return p.advanceCodePoint(),[G.CloseParen,")",p.representationStart,p.representationEnd,void 0];case T:return p.advanceCodePoint(),[G.OpenSquare,"[",p.representationStart,p.representationEnd,void 0];case q:return p.advanceCodePoint(),[G.CloseSquare,"]",p.representationStart,p.representationEnd,void 0];case k:return p.advanceCodePoint(),[G.OpenCurly,"{",p.representationStart,p.representationEnd,void 0];case y:return p.advanceCodePoint(),[G.CloseCurly,"}",p.representationStart,p.representationEnd,void 0];case o:case A:return consumeStringToken(C,p);case R:return consumeHashToken(C,p);case L:case S:return checkIfThreeCodePointsWouldStartANumber(p)?consumeNumericToken(C,p):(p.advanceCodePoint(),[G.Delim,p.source[p.representationStart],p.representationStart,p.representationEnd,{value:p.source[p.representationStart]}]);case g:case i:case P:case s:case H:return consumeWhiteSpace(p);case l:return checkIfThreeCodePointsWouldStartANumber(p)?consumeNumericToken(C,p):checkIfThreeCodePointsWouldStartCDC(p)?(p.advanceCodePoint(3),[G.CDC,"--\x3e",p.representationStart,p.representationEnd,void 0]):checkIfThreeCodePointsWouldStartAnIdentSequence(0,p)?consumeIdentLikeToken(C,p):(p.advanceCodePoint(),[G.Delim,"-",p.representationStart,p.representationEnd,{value:"-"}]);case v:return checkIfFourCodePointsWouldStartCDO(p)?(p.advanceCodePoint(4),[G.CDO,"\x3c!--",p.representationStart,p.representationEnd,void 0]):(p.advanceCodePoint(),[G.Delim,"<",p.representationStart,p.representationEnd,{value:"<"}]);case u:if(p.advanceCodePoint(),checkIfThreeCodePointsWouldStartAnIdentSequence(0,p)){const e=consumeIdentSequence(C,p);return[G.AtKeyword,p.source.slice(p.representationStart,p.representationEnd+1),p.representationStart,p.representationEnd,{value:String.fromCodePoint(...e)}]}return[G.Delim,"@",p.representationStart,p.representationEnd,{value:"@"}];case W:{if(checkIfTwoCodePointsAreAValidEscape(p))return consumeIdentLikeToken(C,p);p.advanceCodePoint();const n=[G.Delim,"\\",p.representationStart,p.representationEnd,{value:"\\"}];return C.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceAfterBackslash,p.representationStart,p.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],n)),n}}return p.advanceCodePoint(),[G.Delim,p.source[p.representationStart],p.representationStart,p.representationEnd,{value:p.source[p.representationStart]}]},endOfFile:function endOfFile(){return void 0===p.codePointSource[p.cursor]}}}function noop(){}function mutateIdent(e,n){const o=[];for(const e of n)o.push(e.codePointAt(0));const t=String.fromCodePoint(...ensureThatValueRoundTripsAsIdent(o));e[1]=t,e[4].value=n}function mutateUnit(e,n){const o=[];for(const e of n)o.push(e.codePointAt(0));const t=ensureThatValueRoundTripsAsIdent(o);101===t[0]&&insertEscapedCodePoint(t,0,t[0]);const r=String.fromCodePoint(...t),i="+"===e[4].signCharacter?e[4].signCharacter:"",s=e[4].value.toString();e[1]=`${i}${s}${r}`,e[4].unit=n}function ensureThatValueRoundTripsAsIdent(e){let n=0;e[0]===l&&e[1]===l?n=2:e[0]===l&&e[1]?(n=2,isIdentStartCodePoint(e[1])||(n+=insertEscapedCodePoint(e,1,e[1]))):isIdentStartCodePoint(e[0])?n=1:(n=1,n+=insertEscapedCodePoint(e,0,e[0]));for(let o=n;o<e.length;o++)isIdentCodePoint(e[o])||(o+=insertEscapedCodePoint(e,o,e[o]));return e}function insertEscapedCodePoint(e,n,o){const t=o.toString(16),r=[];for(const e of t)r.push(e.codePointAt(0));const i=e[n+1];return n===e.length-1||i&&isHexDigitCodePoint(i)?(e.splice(n,1,92,...r,32),1+r.length):(e.splice(n,1,92,...r),r.length)}!function(e){e.Comment="comment",e.AtKeyword="at-keyword-token",e.BadString="bad-string-token",e.BadURL="bad-url-token",e.CDC="CDC-token",e.CDO="CDO-token",e.Colon="colon-token",e.Comma="comma-token",e.Delim="delim-token",e.Dimension="dimension-token",e.EOF="EOF-token",e.Function="function-token",e.Hash="hash-token",e.Ident="ident-token",e.Number="number-token",e.Percentage="percentage-token",e.Semicolon="semicolon-token",e.String="string-token",e.URL="url-token",e.Whitespace="whitespace-token",e.OpenParen="(-token",e.CloseParen=")-token",e.OpenSquare="[-token",e.CloseSquare="]-token",e.OpenCurly="{-token",e.CloseCurly="}-token",e.UnicodeRange="unicode-range-token"}(G||(G={})),function(e){e.Integer="integer",e.Number="number"}(X||(X={})),function(e){e.Unrestricted="unrestricted",e.ID="id"}(Y||(Y={}));const Z=Object.values(G);function isToken(e){return!!Array.isArray(e)&&(!(e.length<4)&&(!!Z.includes(e[0])&&("string"==typeof e[1]&&("number"==typeof e[2]&&"number"==typeof e[3]))))}function isTokenNumeric(e){switch(e[0]){case G.Dimension:case G.Number:case G.Percentage:return!0;default:return!1}}function isTokenWhiteSpaceOrComment(e){switch(e[0]){case G.Whitespace:case G.Comment:return!0;default:return!1}}function isTokenAtKeyword(e){return!!e&&e[0]===G.AtKeyword}function isTokenBadString(e){return!!e&&e[0]===G.BadString}function isTokenBadURL(e){return!!e&&e[0]===G.BadURL}function isTokenCDC(e){return!!e&&e[0]===G.CDC}function isTokenCDO(e){return!!e&&e[0]===G.CDO}function isTokenColon(e){return!!e&&e[0]===G.Colon}function isTokenComma(e){return!!e&&e[0]===G.Comma}function isTokenComment(e){return!!e&&e[0]===G.Comment}function isTokenDelim(e){return!!e&&e[0]===G.Delim}function isTokenDimension(e){return!!e&&e[0]===G.Dimension}function isTokenEOF(e){return!!e&&e[0]===G.EOF}function isTokenFunction(e){return!!e&&e[0]===G.Function}function isTokenHash(e){return!!e&&e[0]===G.Hash}function isTokenIdent(e){return!!e&&e[0]===G.Ident}function isTokenNumber(e){return!!e&&e[0]===G.Number}function isTokenPercentage(e){return!!e&&e[0]===G.Percentage}function isTokenSemicolon(e){return!!e&&e[0]===G.Semicolon}function isTokenString(e){return!!e&&e[0]===G.String}function isTokenURL(e){return!!e&&e[0]===G.URL}function isTokenWhitespace(e){return!!e&&e[0]===G.Whitespace}function isTokenOpenParen(e){return!!e&&e[0]===G.OpenParen}function isTokenCloseParen(e){return!!e&&e[0]===G.CloseParen}function isTokenOpenSquare(e){return!!e&&e[0]===G.OpenSquare}function isTokenCloseSquare(e){return!!e&&e[0]===G.CloseSquare}function isTokenOpenCurly(e){return!!e&&e[0]===G.OpenCurly}function isTokenCloseCurly(e){return!!e&&e[0]===G.CloseCurly}function isTokenUnicodeRange(e){return!!e&&e[0]===G.UnicodeRange}export{Y as HashType,X as NumberType,ParseError,e as ParseErrorMessage,ParseErrorWithToken,Reader,G as TokenType,cloneTokens,isToken,isTokenAtKeyword,isTokenBadString,isTokenBadURL,isTokenCDC,isTokenCDO,isTokenCloseCurly,isTokenCloseParen,isTokenCloseSquare,isTokenColon,isTokenComma,isTokenComment,isTokenDelim,isTokenDimension,isTokenEOF,isTokenFunction,isTokenHash,isTokenIdent,isTokenNumber,isTokenNumeric,isTokenOpenCurly,isTokenOpenParen,isTokenOpenSquare,isTokenPercentage,isTokenSemicolon,isTokenString,isTokenURL,isTokenUnicodeRange,isTokenWhiteSpaceOrComment,isTokenWhitespace,mirrorVariant,mirrorVariantType,mutateIdent,mutateUnit,stringify,tokenize,tokenizer};
