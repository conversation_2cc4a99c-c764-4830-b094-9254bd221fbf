<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
/**
 * Strings for component 'enrol_stripepaymentpro', language 'en'.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
$string['action'] = 'Action';
$string['actions'] = 'Actions';
$string['activelicense'] = 'Activate License';
$string['allcoupons'] = 'All Coupons';
$string['apikey'] = 'API Key';
$string['stripepaymentprocouponmanagementmenuname'] = 'Stripe Payment Pro - Coupon Management';
$string['apikey_desc'] = ' from shop';
$string['applycode'] = 'Apply Code';
$string['areyousure'] = ' Are you sure?';
$string['areyousure_des'] = ' If you cancel the subscription, you will lose access course updates and premium support.';
$string['assignrole'] = 'Assign role';
$string['buynow'] = 'Buy Now';
$string['loadpaymentform'] = 'Load Payment Form';
$string['paymentsuccessful'] = 'Payment Successful';
$string['paymentsuccessfulmessage'] = 'Your payment has been processed successfully and you have been enrolled in the course.';
$string['continuetocourse'] = 'Continue to Course';
$string['paymentfailed'] = 'Payment Failed';
$string['invalidpaymentsession'] = 'Invalid payment session';
$string['paymenterror'] = 'Payment Error';
$string['cancelfailed'] = 'Subscription cancelation failed.';
$string['cancelsubcription'] = 'Cancel Subcription';
$string['confermcancelsubcription'] = 'Conferm Cancel Subcription';
$string['cancelsucess'] = 'Subscription successfully cancelled.';
$string['canntenrol'] = 'Enrolment is disabled or inactive';
$string['chargedescription1'] = "create customer for email receipt";
$string['chargedescription2'] = 'Charge for Course Enrolment Cost.';
$string['checking'] = ' checking...';
$string['connectionfailed'] = 'Network communication with Stripe failed';
$string['cost'] = 'Sign up fee';
$string['costerror'] = 'The enrolment cost is not numeric';
$string['couponapplied'] = 'Coupon Applied';
$string['couponcode'] = 'Coupon Code';
$string['couponcodedescription'] = 'If you have any coupon please apply here';
$string['couponnotvalidforthisproduct'] = 'This coupon is not valid for the selected course or product.';
$string['couponname'] = 'Coupon Name';
$string['couponhelptext'] = 'Enter a valid coupon code to apply discount';
$string['couponverifying'] = 'Verifying...';
$string['coursename'] = 'Course Name';
$string['createnewcoupon'] = 'Create New Coupon';
$string['createusertoken'] = 'REQUIRED: To make Stripe callback work, you must enable Moodle REST protocol on your site';
$string['currency'] = 'Currency';
$string['deactivelicense'] = 'Deactivate License';
$string['defaultrole'] = 'Default role assignment';
$string['defaultroledesc'] = 'Select role which should be assigned to users during Stripe enrolments';
$string['deleteall'] = 'Delete All';
$string['deleteallcoupons'] = 'Delete All Coupons';
$string['discountamount'] = 'Discount Amount';
$string['enablecouponsection'] = 'Enable coupon section';
$string['enableautomatictax'] = 'Enable automatic tax';
$string['enabledrestprotocol'] = ' You must also create a token of moodle_enrol_stripepayment service with Administrator privilege ';
$string['enrol'] = 'Enrol';
$string['enrolbtncolor'] = 'Choose Enroll button Color';
$string['enrolbtncolor_des'] = 'Choose your own custom Color scheme for the Enroll Button.';
$string['entercouponcode'] = 'Enter coupon code';
$string['enrolenddate'] = 'End date';
$string['enrolenddate_help'] = 'If enabled, users can be enrolled until this date only.';
$string['enrolenddaterror'] = 'Enrolment end date cannot be earlier than start date';
$string['enrollsuccess'] = 'Thankyou! Now you are enrolled into the course ';
$string['enrolperiod'] = 'Enrolment duration';
$string['enrolperiod_desc'] = 'Default length of time that the enrolment is valid. If set to zero, the enrolment duration will be unlimited by default.';
$string['enrolperiod_help'] = 'Length of time that the enrolment is valid, starting with the moment the user is enrolled. If disabled, the enrolment duration will be unlimited.';
$string['enrolstartdate'] = 'Start date';
$string['enrolstartdate_help'] = 'If enabled, users can be enrolled from this date onward only.';
$string['error'] = 'Error! ';
$string['expired'] = 'Expired ';
$string['expiredaction'] = 'Enrolment expiration action';
$string['expiredaction_help'] = 'Select action to carry out when user enrolment expires. Please note that some user data and settings are purged from course during course unenrolment.';
$string['finalcost'] = 'Final Cost';
$string['fromhere'] = 'from here';
$string['generalsettings'] = 'General Settings';
$string['generatecoupons'] = 'Generate Coupons';
$string['invalidcontextid'] = 'Not a valid context id! ';
$string['invalidcoupon'] = 'Invalid coupon!';
$string['invalidcouponcode'] = 'Invalid Coupon Code';
$string['invalidcouponcodevalue'] = 'Coupon Code {$a} is not valid!';
$string['invalidcourseid'] = 'Not a valid course id!';
$string['invalidinstance'] = 'Not a valid instance id!';
$string['invalidrequest'] = 'Invalid Request!';
$string['invalidstripeparam'] = 'Invalid parameters were supplied to Stripe API';
$string['invaliduserid'] = 'Not a valid user id! ';
$string['licenseexdate'] = 'License Expiry Date';
$string['licensesetting'] = 'License Settings';
$string['licensestatus'] = 'License Status:';
$string['mailadmins'] = 'Notify admin';
$string['mailstudents'] = 'Notify students';
$string['mailteachers'] = 'Notify teachers';
$string['maxenrolled'] = 'Max enrolled users';
$string['maxenrolled_help'] = 'Specifies the maximum number of users that can stripepaymentpro enrol. 0 means no limit.';
$string['maxenrolledreached'] = 'Maximum number of users allowed to stripepaymentpro-enrol was already reached.';
$string['maxenrolledhelp'] = 'Stripe enrolment messages';
$string['messageprovider:stripepaymentpro_enrolment'] = 'Message Provider';
$string['newcost'] = 'New Cost';
$string['noactivationremain'] = 'No Activations Remaining ';
$string['noapi'] = 'Enter API Key';
$string['nocost'] = 'There is no recurring cost associated with enrolling in this course!';
$string['notstripeerror'] = 'Something else happened, completely unrelated to Stripe';
$string['pleasewait'] = 'Please wait...';
$string['pluginname'] = 'Stripe Payment Pro';
$string['pluginname_desc'] = 'The Stripe module allows you to set up paid courses.  If the cost for any course is zero, then students are not asked to pay for entry.  There is a site-wide cost that you set here as a default for the whole site and then a course setting that you can set for each course individually. The course cost overrides the site cost.';
$string['productid'] = 'Product ID';
$string['productid_desc'] = 'The parchesed ';
$string['publishablekey'] = 'Stripe Publishable Key';
$string['publishablekey_desc'] = 'The API Publishable Key of Stripe account';
$string['recurringtotal'] = 'Recurring Total';
$string['renewalcost'] = 'Renewal Cost';
$string['renewalintarval'] = 'Renewal Interval';
$string['renewalintarvalnum'] = 'Renewal Interval Period';
$string['renewalintarvalnum_help'] = 'The number of intervals between subscription billings. For example, Intarval=month and Intarval Period=3 bills every 3 months. Maximum of one year interval allowed (1 year, 12 months, or 52 weeks).';
$string['renewalintarvalnumerror'] = 'The renewal interval period must be a positive number';
$string['renewnow'] = 'Renew Now ';
$string['secretkey'] = 'Stripe Secret Key';
$string['secretkey_desc'] = 'The API Secret Key of Stripe account';
$string['sessioncreated'] = 'Checkout Session created successfully!';
$string['sessioncreatefail'] = 'Checkout Session creation failed! ';
$string['status_desc'] = 'Allow users to use Stripe to enrol into a course by default.';
$string['stripe:config'] = 'Configure Stripe enrol instances';
$string['stripe:manage'] = 'Manage enrolled users';
$string['stripe:unenrol'] = 'Unenrol users from course';
$string['stripe:unenrolself'] = 'Unenrol self from the course';
$string['stripesorry'] = "Sorry, you can not use the script that way.";
$string['stripeaccepted'] = 'Stripe payments accepted';
$string['stripeauthfail'] = 'Authentication with Stripe API failed';
$string['stripeerror'] = 'Stripe Error ';
$string['stripepaymentpro:config'] = 'Configure stripepaymentpro';
$string['stripepaymentpro:manage'] = 'Manage stripepaymentpro';
$string['stripepaymentpro:unenrol'] = 'Unenrol stripepaymentpro';
$string['stripepaymentpro:unenrolself'] = 'Unenrolself stripepaymentpro';
$string['stripesettings'] = 'Stripe Payment Settings';
$string['stripesettings_des'] = 'Kindly set the following keys on our main plugin <a href="' . $CFG->wwwroot . '/admin/settings.php?section=enrolsettingsstripepayment" target="_blank"> Stripe Payment </a>to this Pro to work';
$string['subid'] = ' Subscription ID';
$string['subscriptionurl'] = 'https://dualcube.com/my-account/api-keys/';
$string['subscriptionrenewurl'] = 'https://dualcube.com/my-account/view-subscription/';
$string['subscriptiontable'] = 'Subscription Table';
$string['subtotal'] = 'Subtotal';
$string['sumthingwrong'] = 'Something went wrong';
$string['taskend'] = 'enrol stripepaymentpro unenrol task task finished';
$string['taskstart'] = 'Hey, admin enrol stripepaymentpro unenrol task is running';
$string['thankscontinue'] = ' Thanks for continuation.';
$string['token_empty_error'] = 'Web service token could not be empty';
$string['trialperiod'] = 'Trial Period (min 2 days)';
$string['unenrol'] = ' got unenroled from ';
$string['unenrolselfconfirm'] = 'Do you really want to unenrol yourself from course "{$a}"?';
$string['unmatchedcourse'] = 'Course Id does not match to the course settings, received: ';
$string['username'] = ' User Name';
$string['webservicetokenstring'] = 'User Token';
$string['invalidsubid'] = ' Given sabcription ID is invalid';
$string['paymentcreatefail'] = 'payment intent create faild';
$string['stripecheckout'] = 'Stripe Checkout';
$string['stripeelements'] = 'Stripe Elements';
$string['paymentgatewaytype_desc'] = 'Choose the type of Stripe payment gateway to use:';
$string['paymentgatewaytype'] = 'Payment Gateway Type';
$string['status'] = 'Status';
$string['startdate'] = 'Start date';
$string['notactive'] = 'Inactive';
$string['notapplicable'] = 'N/A';
$string['nextpayment'] = 'Next payment';
$string['couponsettingsdesc'] = 'Generate and manage Stripe coupon codes directly from your platform. Easily create assign coupons from ';
$string['couponsettings'] = 'Coupon Creation & Assignment';
$string['duplicatedata'] = 'Duplicate data submited';
$string['course'] = 'course';
$string['coupon'] = 'coupon';
$string['addanother'] = 'addanother';
$string['generatecoupon'] = 'Generate coupon';
$string['coupondurationinmonth'] = 'Coupon duration (months)';
$string['couponpercentoff'] = 'Coupon percentage off';
$string['addcouponsetting'] = 'Add coupon';
$string['invalidduration'] = 'Invalid coupon duration.';
$string['invalidpercentoff'] = 'Invalid coupon percentage off.';
$string['couponcreatedsuccess'] = 'Coupon created successfully.';
$string['couponsettinglinktext'] = 'here';
$string['generatecoupon_desc'] = 'Go to the generate coupon page:';
$string['addcoupontocourse'] = 'Add coupon to course';
$string['notvalidcouponforcourse'] = 'This coupon is not valid for this course';
$string['deactivateallcoupons'] = 'Deactivate all coupons';
$string['recurringcost'] = 'Recurring cost';
$string['subscriptionplan'] = 'Subscription plan';
$string['nextrenewal']= 'Next renewal';
$string['cancelsuccess'] = 'subscription cancellation successful';
$string['allcoupons'] = 'All Coupons';
$string['generatecoupons'] = 'Generate Coupon';
$string['assigncoupon'] = 'Assign Coupon';
$string['nocouponfound'] = 'You have not added any coupon yet!!';
$string['couponname'] = 'Coupon name';
$string['couponnamerequired'] = 'Coupon name cant be empty';
$string['coupontypes'] = 'Discount types';
$string['discounttypefixed'] = 'Fixed discount';
$string['discounttypepercentage'] = 'Percentage discount';
$string['discountamount'] = 'Discount amount';
$string['discountamountnonzero'] = 'Discount value must be grater than 0';
$string['couponexpiry'] = 'Coupon expiry';
$string['couponduration'] = 'Duration';
$string['coupondurationforever'] = 'Forever';
$string['coupondurationonce'] = 'Once';
$string['coupondurationmultiplemonths'] = 'Multiple months';
$string['coupondurationmultiplemonthsval'] = 'Number of months';
$string['coupondurationmultiplemonthsvalnonzero'] = 'Number of months should be more than 0';
$string['couponcurrency'] = 'Coupon currency';
$string['couponforallcourses'] = 'All Courses';
$string['couponcourseassignment'] = 'Coupon for course';
$string['subscriptionstatus'] = 'Stripe Subscription Status';
$string['mysubscription'] = 'My Subscription';
$string['couponmanagement'] = 'Stripe Coupon Management';
$string['nosubscriptionsfound'] = 'No subscriptions found.';
$string['coursename'] = 'Course Name';
$string['coupons'] = 'Coupons';
$string['couponid'] = 'Coupon ID';
$string['actions'] = 'Actions';
$string['couponcreatedsuccess'] = 'Coupon created successfully';
$string['invalidsessionid'] = 'Invalid session ID';
$string['stripeerror'] = 'Stripe error: {$a}';
$string['paymentnotsuccessful'] = 'Payment was not successful. Please try again.';
$string['couponnamerequired'] = 'Coupon name is required';
$string['invalidcouponexpiry'] = 'Invalid coupon expiry date';
$string['invalidcouponamount'] = 'Invalid coupon amount';
$string['invaliddiscountpercentage'] = 'Discount percentage must be less than 100';
$string['couponcoderequired'] = 'Please enter a coupon code';
$string['coupon_code_too_short'] = 'Coupon code must be at least 3 characters long';
$string['coupon_code_too_long'] = 'Coupon code is too long (maximum 50 characters)';
$string['coupon_code_invalid_chars'] = 'Coupon code contains invalid characters. Only letters, numbers, hyphens and underscores are allowed';
$string['coupon_not_found'] = 'Coupon not found';
$string['coupon_expired'] = 'This coupon has expired';
$string['coupon_not_applicable'] = 'This coupon is not applicable to this course';
$string['coupon_usage_limit_reached'] = 'This coupon has reached its usage limit';
$string['coupon_not_valid'] = 'This coupon is no longer valid';
$string['coupon_validation_error'] = 'Error validating coupon. Please try again';
$string['coupon_applied_successfully'] = 'Coupon applied successfully!';
$string['coupon_course_specific_warning'] = 'This coupon is for a specific course. Verifying applicability...';
$string['invalidcoupon'] = 'Invalid coupon code';
$string['coupon_help_text'] = 'Enter a valid coupon code to receive a discount on your enrollment';
$string['checking'] = 'Checking...';
$string['allcoupons'] = 'All Coupons';
$string['generatecoupons'] = 'Generate Coupons';
$string['existing_coupons'] = 'Existing Coupons';
$string['createnewcoupon'] = 'Create New Coupon';
$string['nocouponfound'] = 'No coupons found. Create your first coupon using the "Generate Coupons" tab.';
$string['deactivateallcoupons'] = 'Delete All';
$string['subscribe_to_enrolment'] = 'Subscribe to Enrolment in course {$a}';
$string['due_today'] = 'due today';
$string['days_free'] = '{$a} days free';
$string['then_amount_per_interval'] = 'Then {$a->amount} / {$a->interval_count} {$a->interval} after';
$string['then_amount_every_interval'] = 'Then {$a->amount} every {$a->interval_count} {$a->interval}';
$string['enrolment_in_course'] = 'Enrolment in course {$a}';
$string['course_enrolment_for'] = 'Course enrolment for {$a}';
$string['billed_every_interval'] = 'Billed every {$a->interval_count} {$a->interval}';
$string['subtotal'] = 'Subtotal';
$string['total_due_today'] = 'Total due today';
$string['view_details'] = 'View Details';
$string['discount'] = 'Discount';
$string['discount_applied'] = 'Discount applied';
$string['coupons'] = 'coupon(s)';
$string['click_to_copy_coupon'] = 'Click to copy coupon ID';
$string['deactivatecoupon'] = 'Deactivate this coupon';
$string['copied'] = 'Copied!';
$string['failed'] = 'Failed';
$string['areyousuredeletecoupon'] = 'Are you sure you want to delete this coupon? This will remove it from both the database and Stripe.';
$string['areyousuredeleteallcoupons'] = 'Are you sure you want to delete ALL coupons for this course? This will remove them from both the database and Stripe.';
$string['errordeletingcoupon'] = 'Error deleting coupon: {$a}';
$string['errordeletingcoupons'] = 'Error deleting coupons: {$a}';
$string['successmessage'] = 'Success: {$a}';
$string['unknown_error'] = 'Unknown error';
$string['search_coupons'] = 'Search coupons';
$string['search_coupons_placeholder'] = 'Search by course name, coupon name, or discount amount...';
$string['search_help_text'] = 'Search across course names, coupon codes, and discount amounts';
$string['nosearchresults'] = 'No coupons found matching your search criteria';
$string['clear_search'] = 'Clear search';

// Receipt URL Download strings
$string['stripepaymentpro_receiptdownload_menu_name'] = 'Stripe Payment Pro - Receipt URL Download';
$string['receipt_management'] = 'Receipt URL Download';
$string['receipturl_download'] = 'Receipt URL Download';
$string['studentname'] = 'Student Name';
$string['paymentdate'] = 'Payment Date';
$string['receipturl'] = 'Receipt URL';
$string['receipturl_available'] = 'Receipt Available';
$string['no_receipturl'] = 'No Receipt';
$string['download'] = 'Download';
$string['download_receipt'] = 'Download Receipt';
$string['click_to_copy_receipturl'] = 'Click to copy receipt URL';
$string['search_receipts'] = 'Search receipts';
$string['search_receipts_placeholder'] = 'Search by course name, student name, or email...';
$string['no_receipts_found'] = 'No receipts with URLs found. Receipt URLs are automatically captured when payments are processed through Stripe.';
$string['copied'] = 'Copied!';