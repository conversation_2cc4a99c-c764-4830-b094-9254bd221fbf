<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="enrol/stripepaymentpro/db" VERSION="20120122" COMMENT="XMLDB file for Moodle enrol/stripepaymentpro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
  <TABLES>
    <TABLE NAME="enrol_stripepaymentpro" COMMENT="Holds all known information about Stripe transactions">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" />
        <FIELD NAME="receiveremail" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="receiverid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="itemname" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="instanceid" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="couponid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="stripeemail" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="currency" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="memo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="tax" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="paymentstatus" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="pendingreason" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="txnid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="productid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="productname" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="producttype" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="subscriptionid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="renewalinterval" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="renewalintervalperiod" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="trialperiodend" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="receipturl" TYPE="char" LENGTH="500" NOTNULL="false" SEQUENCE="false" />
        <FIELD NAME="timeupdated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id" />
      </KEYS>
    </TABLE>

    <TABLE NAME="enrol_stripepaymentpro_coupons" COMMENT="Associates coupons with courses">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" />
        <FIELD NAME="couponid" TYPE="char" LENGTH="255" NOTNULL="true" />
        <FIELD NAME="couponname" TYPE="char" LENGTH="255" NOTNULL="true" />
        <FIELD NAME="amountoff" TYPE="float" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="2" />
        <FIELD NAME="percentoff" TYPE="float" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="2" />
        <FIELD NAME="currency" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="duration" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="noofmonths" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="stripeproductid" TYPE="char" LENGTH="255" NOTNULL="false" />
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
        <FIELD NAME="couponexpiry" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" />
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id" />
        <KEY NAME="unique_couponid" TYPE="unique" FIELDS="couponid" />
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>