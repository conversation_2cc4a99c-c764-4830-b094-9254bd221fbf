"use strict";var e,t=require("@csstools/css-parser-algorithms"),i=require("@csstools/css-tokenizer");exports.NodeType=void 0,(e=exports.NodeType||(exports.NodeType={})).CustomMedia="custom-media",e.GeneralEnclosed="general-enclosed",e.MediaAnd="media-and",e.MediaCondition="media-condition",e.MediaConditionListWithAnd="media-condition-list-and",e.MediaConditionListWithOr="media-condition-list-or",e.MediaFeature="media-feature",e.MediaFeatureBoolean="mf-boolean",e.MediaFeatureName="mf-name",e.MediaFeaturePlain="mf-plain",e.MediaFeatureRangeNameValue="mf-range-name-value",e.MediaFeatureRangeValueName="mf-range-value-name",e.MediaFeatureRangeValueNameValue="mf-range-value-name-value",e.MediaFeatureValue="mf-value",e.MediaInParens="media-in-parens",e.MediaNot="media-not",e.MediaOr="media-or",e.MediaQueryWithType="media-query-with-type",e.MediaQueryWithoutType="media-query-without-type",e.MediaQueryInvalid="media-query-invalid";class MediaCondition{type=exports.NodeType.MediaCondition;media;constructor(e){this.media=e}tokens(){return this.media.tokens()}toString(){return this.media.toString()}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,media:this.media.toJSON()}}isMediaCondition(){return MediaCondition.isMediaCondition(this)}static isMediaCondition(e){return!!e&&(e instanceof MediaCondition&&e.type===exports.NodeType.MediaCondition)}}class MediaInParens{type=exports.NodeType.MediaInParens;media;before;after;constructor(e,t=[],i=[]){this.media=e,this.before=t,this.after=i}tokens(){return[...this.before,...this.media.tokens(),...this.after]}toString(){return i.stringify(...this.before)+this.media.toString()+i.stringify(...this.after)}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&("walk"in this.media?this.media.walk(e,i):void 0)}toJSON(){return{type:this.type,media:this.media.toJSON(),before:this.before,after:this.after}}isMediaInParens(){return MediaInParens.isMediaInParens(this)}static isMediaInParens(e){return!!e&&(e instanceof MediaInParens&&e.type===exports.NodeType.MediaInParens)}}class MediaQueryWithType{type=exports.NodeType.MediaQueryWithType;modifier;mediaType;and=void 0;media=void 0;constructor(e,t,i,a){this.modifier=e,this.mediaType=t,i&&a&&(this.and=i,this.media=a)}getModifier(){if(!this.modifier.length)return"";for(let e=0;e<this.modifier.length;e++){const t=this.modifier[e];if(i.isTokenIdent(t))return t[4].value}return""}negateQuery(){const e=new MediaQueryWithType([...this.modifier],[...this.mediaType],this.and,this.media);if(0===e.modifier.length)return e.modifier=[[i.TokenType.Ident,"not",-1,-1,{value:"not"}],[i.TokenType.Whitespace," ",-1,-1,void 0]],e;for(let t=0;t<e.modifier.length;t++){const a=e.modifier[t];if(i.isTokenIdent(a)&&"not"===a[4].value.toLowerCase()){e.modifier.splice(t,1);break}if(i.isTokenIdent(a)&&"only"===a[4].value.toLowerCase()){a[1]="not",a[4].value="not";break}}return e}getMediaType(){if(!this.mediaType.length)return"";for(let e=0;e<this.mediaType.length;e++){const t=this.mediaType[e];if(i.isTokenIdent(t))return t[4].value}return""}tokens(){return this.and&&this.media?[...this.modifier,...this.mediaType,...this.and,...this.media.tokens()]:[...this.modifier,...this.mediaType]}toString(){return this.and&&this.media?i.stringify(...this.modifier)+i.stringify(...this.mediaType)+i.stringify(...this.and)+this.media.toString():i.stringify(...this.modifier)+i.stringify(...this.mediaType)}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;if(t&&(i={...t}),this.media)return!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,string:this.toString(),modifier:this.modifier,mediaType:this.mediaType,and:this.and,media:this.media}}isMediaQueryWithType(){return MediaQueryWithType.isMediaQueryWithType(this)}static isMediaQueryWithType(e){return!!e&&(e instanceof MediaQueryWithType&&e.type===exports.NodeType.MediaQueryWithType)}}class MediaQueryWithoutType{type=exports.NodeType.MediaQueryWithoutType;media;constructor(e){this.media=e}negateQuery(){let e=this.media;if(e.media.type===exports.NodeType.MediaNot)return new MediaQueryWithoutType(new MediaCondition(e.media.media));e.media.type===exports.NodeType.MediaConditionListWithOr&&(e=new MediaCondition(new MediaInParens(e,[[i.TokenType.Whitespace," ",0,0,void 0],[i.TokenType.OpenParen,"(",0,0,void 0]],[[i.TokenType.CloseParen,")",0,0,void 0]])));return new MediaQueryWithType([[i.TokenType.Ident,"not",0,0,{value:"not"}],[i.TokenType.Whitespace," ",0,0,void 0]],[[i.TokenType.Ident,"all",0,0,{value:"all"}],[i.TokenType.Whitespace," ",0,0,void 0]],[[i.TokenType.Ident,"and",0,0,{value:"and"}]],e)}tokens(){return this.media.tokens()}toString(){return this.media.toString()}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,string:this.toString(),media:this.media}}isMediaQueryWithoutType(){return MediaQueryWithoutType.isMediaQueryWithoutType(this)}static isMediaQueryWithoutType(e){return!!e&&(e instanceof MediaQueryWithoutType&&e.type===exports.NodeType.MediaQueryWithoutType)}}class MediaQueryInvalid{type=exports.NodeType.MediaQueryInvalid;media;constructor(e){this.media=e}negateQuery(){return new MediaQueryInvalid(this.media)}tokens(){return this.media.flatMap((e=>e.tokens()))}toString(){return this.media.map((e=>e.toString())).join("")}walk(e,i){if(0===this.media.length)return;const a=t.walkerIndexGenerator(this.media);let r=0;for(;r<this.media.length;){const t=this.media[r];let n;if(i&&(n={...i}),!1===e({node:t,parent:this,state:n},r))return!1;if("walk"in t&&this.media.includes(t)&&!1===t.walk(e,n))return!1;if(r=a(this.media,t,r),-1===r)break}}toJSON(){return{type:this.type,string:this.toString(),media:this.media}}isMediaQueryInvalid(){return MediaQueryInvalid.isMediaQueryInvalid(this)}static isMediaQueryInvalid(e){return!!e&&(e instanceof MediaQueryInvalid&&e.type===exports.NodeType.MediaQueryInvalid)}}class GeneralEnclosed{type=exports.NodeType.GeneralEnclosed;value;constructor(e){this.value=e}tokens(){return this.value.tokens()}toString(){return this.value.toString()}indexOf(e){return e===this.value?"value":-1}at(e){if("value"===e)return this.value}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.value,parent:this,state:i},"value")&&("walk"in this.value?this.value.walk(e,i):void 0)}toJSON(){return{type:this.type,tokens:this.tokens()}}isGeneralEnclosed(){return GeneralEnclosed.isGeneralEnclosed(this)}static isGeneralEnclosed(e){return!!e&&(e instanceof GeneralEnclosed&&e.type===exports.NodeType.GeneralEnclosed)}}class MediaAnd{type=exports.NodeType.MediaAnd;modifier;media;constructor(e,t){this.modifier=e,this.media=t}tokens(){return[...this.modifier,...this.media.tokens()]}toString(){return i.stringify(...this.modifier)+this.media.toString()}indexOf(e){return e===this.media?"media":-1}at(e){return"media"===e?this.media:null}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,modifier:this.modifier,media:this.media.toJSON()}}isMediaAnd(){return MediaAnd.isMediaAnd(this)}static isMediaAnd(e){return!!e&&(e instanceof MediaAnd&&e.type===exports.NodeType.MediaAnd)}}class MediaConditionListWithAnd{type=exports.NodeType.MediaConditionListWithAnd;leading;list;before;after;constructor(e,t,i=[],a=[]){this.leading=e,this.list=t,this.before=i,this.after=a}tokens(){return[...this.before,...this.leading.tokens(),...this.list.flatMap((e=>e.tokens())),...this.after]}toString(){return i.stringify(...this.before)+this.leading.toString()+this.list.map((e=>e.toString())).join("")+i.stringify(...this.after)}indexOf(e){return e===this.leading?"leading":e.type===exports.NodeType.MediaAnd?this.list.indexOf(e):-1}at(e){return"leading"===e?this.leading:"number"==typeof e?(e<0&&(e=this.list.length+e),this.list[e]):void 0}walk(e,i){let a;if(i&&(a={...i}),!1===e({node:this.leading,parent:this,state:a},"leading"))return!1;if("walk"in this.leading&&!1===this.leading.walk(e,a))return!1;if(0===this.list.length)return;const r=t.walkerIndexGenerator(this.list);let n=0;for(;n<this.list.length;){const t=this.list[n];if(i&&(a={...i}),!1===e({node:t,parent:this,state:a},n))return!1;if("walk"in t&&this.list.includes(t)&&!1===t.walk(e,a))return!1;if(n=r(this.list,t,n),-1===n)break}}toJSON(){return{type:this.type,leading:this.leading.toJSON(),list:this.list.map((e=>e.toJSON())),before:this.before,after:this.after}}isMediaConditionListWithAnd(){return MediaConditionListWithAnd.isMediaConditionListWithAnd(this)}static isMediaConditionListWithAnd(e){return!!e&&(e instanceof MediaConditionListWithAnd&&e.type===exports.NodeType.MediaConditionListWithAnd)}}class MediaConditionListWithOr{type=exports.NodeType.MediaConditionListWithOr;leading;list;before;after;constructor(e,t,i=[],a=[]){this.leading=e,this.list=t,this.before=i,this.after=a}tokens(){return[...this.before,...this.leading.tokens(),...this.list.flatMap((e=>e.tokens())),...this.after]}toString(){return i.stringify(...this.before)+this.leading.toString()+this.list.map((e=>e.toString())).join("")+i.stringify(...this.after)}indexOf(e){return e===this.leading?"leading":e.type===exports.NodeType.MediaOr?this.list.indexOf(e):-1}at(e){return"leading"===e?this.leading:"number"==typeof e?(e<0&&(e=this.list.length+e),this.list[e]):void 0}walk(e,i){let a;if(i&&(a={...i}),!1===e({node:this.leading,parent:this,state:a},"leading"))return!1;if("walk"in this.leading&&!1===this.leading.walk(e,a))return!1;if(0===this.list.length)return;const r=t.walkerIndexGenerator(this.list);let n=0;for(;n<this.list.length;){const t=this.list[n];if(i&&(a={...i}),!1===e({node:t,parent:this,state:a},n))return!1;if("walk"in t&&this.list.includes(t)&&!1===t.walk(e,a))return!1;if(n=r(this.list,t,n),-1===n)break}}toJSON(){return{type:this.type,leading:this.leading.toJSON(),list:this.list.map((e=>e.toJSON())),before:this.before,after:this.after}}isMediaConditionListWithOr(){return MediaConditionListWithOr.isMediaConditionListWithOr(this)}static isMediaConditionListWithOr(e){return!!e&&(e instanceof MediaConditionListWithOr&&e.type===exports.NodeType.MediaConditionListWithOr)}}function isNumber(e){return!!(t.isTokenNode(e)&&i.isTokenNumber(e.value)||t.isFunctionNode(e)&&a.has(e.getName().toLowerCase()))}const a=new Set(["abs","acos","asin","atan","atan2","calc","clamp","cos","exp","hypot","log","max","min","mod","pow","rem","round","sign","sin","sqrt","tan"]);function isDimension(e){return t.isTokenNode(e)&&i.isTokenDimension(e.value)}function isIdent(e){return t.isTokenNode(e)&&i.isTokenIdent(e.value)}function isEnvironmentVariable(e){return t.isFunctionNode(e)&&"env"===e.getName().toLowerCase()}class MediaFeatureName{type=exports.NodeType.MediaFeatureName;name;before;after;constructor(e,t=[],i=[]){this.name=e,this.before=t,this.after=i}getName(){return this.name.value[4].value}getNameToken(){return this.name.value}tokens(){return[...this.before,...this.name.tokens(),...this.after]}toString(){return i.stringify(...this.before)+this.name.toString()+i.stringify(...this.after)}indexOf(e){return e===this.name?"name":-1}at(e){if("name"===e)return this.name}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens()}}isMediaFeatureName(){return MediaFeatureName.isMediaFeatureName(this)}static isMediaFeatureName(e){return!!e&&(e instanceof MediaFeatureName&&e.type===exports.NodeType.MediaFeatureName)}}function parseMediaFeatureName(e){let i=-1;for(let a=0;a<e.length;a++){const r=e[a];if(r.type!==t.ComponentValueType.Whitespace&&r.type!==t.ComponentValueType.Comment){if(!isIdent(r))return!1;if(-1!==i)return!1;i=a}}return-1!==i&&new MediaFeatureName(e[i],e.slice(0,i).flatMap((e=>e.tokens())),e.slice(i+1).flatMap((e=>e.tokens())))}class MediaFeatureBoolean{type=exports.NodeType.MediaFeatureBoolean;name;constructor(e){this.name=e}getName(){return this.name.getName()}getNameToken(){return this.name.getNameToken()}tokens(){return this.name.tokens()}toString(){return this.name.toString()}indexOf(e){return e===this.name?"name":-1}at(e){if("name"===e)return this.name}toJSON(){return{type:this.type,name:this.name.toJSON(),tokens:this.tokens()}}isMediaFeatureBoolean(){return MediaFeatureBoolean.isMediaFeatureBoolean(this)}static isMediaFeatureBoolean(e){return!!e&&(e instanceof MediaFeatureBoolean&&e.type===exports.NodeType.MediaFeatureBoolean)}}function parseMediaFeatureBoolean(e){const t=parseMediaFeatureName(e);return!1===t?t:new MediaFeatureBoolean(t)}class MediaFeatureValue{type=exports.NodeType.MediaFeatureValue;value;before;after;constructor(e,t=[],i=[]){Array.isArray(e)&&1===e.length?this.value=e[0]:this.value=e,this.before=t,this.after=i}tokens(){return Array.isArray(this.value)?[...this.before,...this.value.flatMap((e=>e.tokens())),...this.after]:[...this.before,...this.value.tokens(),...this.after]}toString(){return Array.isArray(this.value)?i.stringify(...this.before)+this.value.map((e=>e.toString())).join("")+i.stringify(...this.after):i.stringify(...this.before)+this.value.toString()+i.stringify(...this.after)}indexOf(e){return e===this.value?"value":-1}at(e){return"value"===e?this.value:Array.isArray(this.value)&&"number"==typeof e?(e<0&&(e=this.value.length+e),this.value[e]):void 0}walk(e,i){if(Array.isArray(this.value)){if(0===this.value.length)return;const a=t.walkerIndexGenerator(this.value);let r=0;for(;r<this.value.length;){const t=this.value[r];let n;if(i&&(n={...i}),!1===e({node:t,parent:this,state:n},r))return!1;if("walk"in t&&this.value.includes(t)&&!1===t.walk(e,n))return!1;if(r=a(this.value,t,r),-1===r)break}}else{let t;if(i&&(t={...i}),!1===e({node:this.value,parent:this,state:t},"value"))return!1;if("walk"in this.value)return this.value.walk(e,t)}}toJSON(){return Array.isArray(this.value)?{type:this.type,value:this.value.map((e=>e.toJSON())),tokens:this.tokens()}:{type:this.type,value:this.value.toJSON(),tokens:this.tokens()}}isMediaFeatureValue(){return MediaFeatureValue.isMediaFeatureValue(this)}static isMediaFeatureValue(e){return!!e&&(e instanceof MediaFeatureValue&&e.type===exports.NodeType.MediaFeatureValue)}}function parseMediaFeatureValue(e,i=!1){let a=-1,r=-1;for(let n=0;n<e.length;n++){const s=e[n];if(s.type!==t.ComponentValueType.Whitespace&&s.type!==t.ComponentValueType.Comment){if(-1!==a)return!1;if(isNumber(s)){const t=matchesRatioExactly(e.slice(n));if(-1!==t){a=t[0]+n,r=t[1]+n,n+=t[1]-t[0];continue}a=n,r=n}else if(isEnvironmentVariable(s))a=n,r=n;else if(isDimension(s))a=n,r=n;else{if(i||!isIdent(s))return!1;a=n,r=n}}}return-1!==a&&new MediaFeatureValue(e.slice(a,r+1),e.slice(0,a).flatMap((e=>e.tokens())),e.slice(r+1).flatMap((e=>e.tokens())))}function matchesRatioExactly(e){let i=-1,a=-1;const r=matchesRatio(e);if(-1===r)return-1;i=r[0],a=r[1];for(let i=a+1;i<e.length;i++){const a=e[i];if(!t.isWhiteSpaceOrCommentNode(a))return-1}return[i,a]}function matchesRatio(e){let a=-1,r=-1;for(let n=0;n<e.length;n++){const s=e[n];if(!t.isWhiteSpaceOrCommentNode(s)){if(t.isTokenNode(s)){const e=s.value;if(i.isTokenDelim(e)&&"/"===e[4].value){if(-1===a)return-1;if(-1!==r)return-1;r=n;continue}}if(!isNumber(s))return-1;if(-1!==r)return[a,n];if(-1!==a)return-1;a=n}}return-1}class MediaFeaturePlain{type=exports.NodeType.MediaFeaturePlain;name;colon;value;constructor(e,t,i){this.name=e,this.colon=t,this.value=i}getName(){return this.name.getName()}getNameToken(){return this.name.getNameToken()}tokens(){return[...this.name.tokens(),this.colon,...this.value.tokens()]}toString(){return this.name.toString()+i.stringify(this.colon)+this.value.toString()}indexOf(e){return e===this.name?"name":e===this.value?"value":-1}at(e){return"name"===e?this.name:"value"===e?this.value:void 0}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.value,parent:this,state:i},"value")&&this.value.walk(e,i)}toJSON(){return{type:this.type,name:this.name.toJSON(),value:this.value.toJSON(),tokens:this.tokens()}}isMediaFeaturePlain(){return MediaFeaturePlain.isMediaFeaturePlain(this)}static isMediaFeaturePlain(e){return!!e&&(e instanceof MediaFeaturePlain&&e.type===exports.NodeType.MediaFeaturePlain)}}function parseMediaFeaturePlain(e){let a=[],r=[],n=null;for(let s=0;s<e.length;s++){const o=e[s];if(o.type===t.ComponentValueType.Token){const t=o.value;if(i.isTokenColon(t)){a=e.slice(0,s),r=e.slice(s+1),n=t;break}}}if(!a.length||!r.length||!n)return!1;const s=parseMediaFeatureName(a);if(!1===s)return!1;const o=parseMediaFeatureValue(r);return!1!==o&&new MediaFeaturePlain(s,n,o)}var r,n,s;function matchesComparison(e){let a=-1;for(let r=0;r<e.length;r++){const n=e[r];if(n.type===t.ComponentValueType.Token){const e=n.value;if(i.isTokenDelim(e)){if(e[4].value===exports.MediaFeatureEQ.EQ)return-1!==a?[a,r]:[r,r];if(e[4].value===exports.MediaFeatureLT.LT){a=r;continue}if(e[4].value===exports.MediaFeatureGT.GT){a=r;continue}}}break}return-1!==a&&[a,a]}function comparisonFromTokens(e){if(1!==e.length&&2!==e.length)return!1;if(!i.isTokenDelim(e[0]))return!1;if(1===e.length)switch(e[0][4].value){case exports.MediaFeatureEQ.EQ:return exports.MediaFeatureEQ.EQ;case exports.MediaFeatureLT.LT:return exports.MediaFeatureLT.LT;case exports.MediaFeatureGT.GT:return exports.MediaFeatureGT.GT;default:return!1}if(!i.isTokenDelim(e[1]))return!1;if(e[1][4].value!==exports.MediaFeatureEQ.EQ)return!1;switch(e[0][4].value){case exports.MediaFeatureLT.LT:return exports.MediaFeatureLT.LT_OR_EQ;case exports.MediaFeatureGT.GT:return exports.MediaFeatureGT.GT_OR_EQ;default:return!1}}exports.MediaFeatureLT=void 0,(r=exports.MediaFeatureLT||(exports.MediaFeatureLT={})).LT="<",r.LT_OR_EQ="<=",exports.MediaFeatureGT=void 0,(n=exports.MediaFeatureGT||(exports.MediaFeatureGT={})).GT=">",n.GT_OR_EQ=">=",exports.MediaFeatureEQ=void 0,(exports.MediaFeatureEQ||(exports.MediaFeatureEQ={})).EQ="=";class MediaFeatureRangeNameValue{type=exports.NodeType.MediaFeatureRangeNameValue;name;operator;value;constructor(e,t,i){this.name=e,this.operator=t,this.value=i}operatorKind(){return comparisonFromTokens(this.operator)}getName(){return this.name.getName()}getNameToken(){return this.name.getNameToken()}tokens(){return[...this.name.tokens(),...this.operator,...this.value.tokens()]}toString(){return this.name.toString()+i.stringify(...this.operator)+this.value.toString()}indexOf(e){return e===this.name?"name":e===this.value?"value":-1}at(e){return"name"===e?this.name:"value"===e?this.value:void 0}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.value,parent:this,state:i},"value")&&("walk"in this.value?this.value.walk(e,i):void 0)}toJSON(){return{type:this.type,name:this.name.toJSON(),value:this.value.toJSON(),tokens:this.tokens()}}isMediaFeatureRangeNameValue(){return MediaFeatureRangeNameValue.isMediaFeatureRangeNameValue(this)}static isMediaFeatureRangeNameValue(e){return!!e&&(e instanceof MediaFeatureRangeNameValue&&e.type===exports.NodeType.MediaFeatureRangeNameValue)}}class MediaFeatureRangeValueName{type=exports.NodeType.MediaFeatureRangeValueName;name;operator;value;constructor(e,t,i){this.name=e,this.operator=t,this.value=i}operatorKind(){return comparisonFromTokens(this.operator)}getName(){return this.name.getName()}getNameToken(){return this.name.getNameToken()}tokens(){return[...this.value.tokens(),...this.operator,...this.name.tokens()]}toString(){return this.value.toString()+i.stringify(...this.operator)+this.name.toString()}indexOf(e){return e===this.name?"name":e===this.value?"value":-1}at(e){return"name"===e?this.name:"value"===e?this.value:void 0}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.value,parent:this,state:i},"value")&&("walk"in this.value?this.value.walk(e,i):void 0)}toJSON(){return{type:this.type,name:this.name.toJSON(),value:this.value.toJSON(),tokens:this.tokens()}}isMediaFeatureRangeValueName(){return MediaFeatureRangeValueName.isMediaFeatureRangeValueName(this)}static isMediaFeatureRangeValueName(e){return!!e&&(e instanceof MediaFeatureRangeValueName&&e.type===exports.NodeType.MediaFeatureRangeValueName)}}class MediaFeatureRangeValueNameValue{type=exports.NodeType.MediaFeatureRangeValueNameValue;name;valueOne;valueOneOperator;valueTwo;valueTwoOperator;constructor(e,t,i,a,r){this.name=e,this.valueOne=t,this.valueOneOperator=i,this.valueTwo=a,this.valueTwoOperator=r}valueOneOperatorKind(){return comparisonFromTokens(this.valueOneOperator)}valueTwoOperatorKind(){return comparisonFromTokens(this.valueTwoOperator)}getName(){return this.name.getName()}getNameToken(){return this.name.getNameToken()}tokens(){return[...this.valueOne.tokens(),...this.valueOneOperator,...this.name.tokens(),...this.valueTwoOperator,...this.valueTwo.tokens()]}toString(){return this.valueOne.toString()+i.stringify(...this.valueOneOperator)+this.name.toString()+i.stringify(...this.valueTwoOperator)+this.valueTwo.toString()}indexOf(e){return e===this.name?"name":e===this.valueOne?"valueOne":e===this.valueTwo?"valueTwo":-1}at(e){return"name"===e?this.name:"valueOne"===e?this.valueOne:"valueTwo"===e?this.valueTwo:void 0}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.valueOne,parent:this,state:i},"valueOne")&&((!("walk"in this.valueOne)||!1!==this.valueOne.walk(e,i))&&(t&&(i={...t}),!1!==e({node:this.valueTwo,parent:this,state:i},"valueTwo")&&((!("walk"in this.valueTwo)||!1!==this.valueTwo.walk(e,i))&&void 0)))}toJSON(){return{type:this.type,name:this.name.toJSON(),valueOne:this.valueOne.toJSON(),valueTwo:this.valueTwo.toJSON(),tokens:this.tokens()}}isMediaFeatureRangeValueNameValue(){return MediaFeatureRangeValueNameValue.isMediaFeatureRangeValueNameValue(this)}static isMediaFeatureRangeValueNameValue(e){return!!e&&(e instanceof MediaFeatureRangeValueNameValue&&e.type===exports.NodeType.MediaFeatureRangeValueNameValue)}}function parseMediaFeatureRange(e){let a=!1,r=!1;for(let n=0;n<e.length;n++){const s=e[n];if(s.type===t.ComponentValueType.Token){const t=s.value;if(i.isTokenDelim(t)){const t=matchesComparison(e.slice(n));if(!1!==t){if(!1!==a){r=[t[0]+n,t[1]+n];break}a=[t[0]+n,t[1]+n],n+=t[1]}}}}if(!1===a)return!1;const n=[e[a[0]].value];if(a[0]!==a[1]&&n.push(e[a[1]].value),!1===r){const t=e.slice(0,a[0]),i=e.slice(a[1]+1),r=parseMediaFeatureName(t);if(r){const e=parseMediaFeatureValue(i,!0);return!!e&&new MediaFeatureRangeNameValue(r,n,e)}const s=parseMediaFeatureName(i);if(s){const e=parseMediaFeatureValue(t,!0);return!!e&&new MediaFeatureRangeValueName(s,n,e)}return!1}const s=[e[r[0]].value];r[0]!==r[1]&&s.push(e[r[1]].value);const o=e.slice(0,a[0]),u=e.slice(a[1]+1,r[0]),d=e.slice(r[1]+1),l=parseMediaFeatureValue(o,!0),p=parseMediaFeatureName(u),h=parseMediaFeatureValue(d,!0);if(!l||!p||!h)return!1;{const e=comparisonFromTokens(n);if(!1===e||e===exports.MediaFeatureEQ.EQ)return!1;const t=comparisonFromTokens(s);if(!1===t||t===exports.MediaFeatureEQ.EQ)return!1;if(!(e!==exports.MediaFeatureLT.LT&&e!==exports.MediaFeatureLT.LT_OR_EQ||t!==exports.MediaFeatureGT.GT&&t!==exports.MediaFeatureGT.GT_OR_EQ))return!1;if(!(e!==exports.MediaFeatureGT.GT&&e!==exports.MediaFeatureGT.GT_OR_EQ||t!==exports.MediaFeatureLT.LT&&t!==exports.MediaFeatureLT.LT_OR_EQ))return!1}return new MediaFeatureRangeValueNameValue(p,l,n,h,s)}class MediaFeature{type=exports.NodeType.MediaFeature;feature;before;after;constructor(e,t=[],i=[]){this.feature=e,this.before=t,this.after=i}getName(){return this.feature.getName()}getNameToken(){return this.feature.getNameToken()}tokens(){return[...this.before,...this.feature.tokens(),...this.after]}toString(){return i.stringify(...this.before)+this.feature.toString()+i.stringify(...this.after)}indexOf(e){return e===this.feature?"feature":-1}at(e){if("feature"===e)return this.feature}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.feature,parent:this,state:i},"feature")&&("walk"in this.feature?this.feature.walk(e,i):void 0)}toJSON(){return{type:this.type,feature:this.feature.toJSON(),before:this.before,after:this.after}}isMediaFeature(){return MediaFeature.isMediaFeature(this)}static isMediaFeature(e){return!!e&&(e instanceof MediaFeature&&e.type===exports.NodeType.MediaFeature)}}function parseMediaFeature(e,t=[],a=[]){if(!i.isTokenOpenParen(e.startToken))return!1;const r=parseMediaFeatureBoolean(e.value);if(!1!==r)return new MediaFeature(r,t,a);const n=parseMediaFeaturePlain(e.value);if(!1!==n)return new MediaFeature(n,t,a);const s=parseMediaFeatureRange(e.value);return!1!==s&&new MediaFeature(s,t,a)}class MediaNot{type=exports.NodeType.MediaNot;modifier;media;constructor(e,t){this.modifier=e,this.media=t}tokens(){return[...this.modifier,...this.media.tokens()]}toString(){return i.stringify(...this.modifier)+this.media.toString()}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,modifier:this.modifier,media:this.media.toJSON()}}isMediaNot(){return MediaNot.isMediaNot(this)}static isMediaNot(e){return!!e&&(e instanceof MediaNot&&e.type===exports.NodeType.MediaNot)}}class MediaOr{type=exports.NodeType.MediaOr;modifier;media;constructor(e,t){this.modifier=e,this.media=t}tokens(){return[...this.modifier,...this.media.tokens()]}toString(){return i.stringify(...this.modifier)+this.media.toString()}indexOf(e){return e===this.media?"media":-1}at(e){if("media"===e)return this.media}walk(e,t){let i;return t&&(i={...t}),!1!==e({node:this.media,parent:this,state:i},"media")&&this.media.walk(e,i)}toJSON(){return{type:this.type,modifier:this.modifier,media:this.media.toJSON()}}isMediaOr(){return MediaOr.isMediaOr(this)}static isMediaOr(e){return!!e&&(e instanceof MediaOr&&e.type===exports.NodeType.MediaOr)}}function modifierFromToken(e){if(!i.isTokenIdent(e))return!1;switch(e[4].value.toLowerCase()){case exports.MediaQueryModifier.Not:return exports.MediaQueryModifier.Not;case exports.MediaQueryModifier.Only:return exports.MediaQueryModifier.Only;default:return!1}}function parseMediaQuery(e){{const t=parseMediaCondition(e);if(!1!==t)return new MediaQueryWithoutType(t)}{let a=-1,r=-1,n=-1;for(let s=0;s<e.length;s++){const o=e[s];if(!t.isWhitespaceNode(o)&&!t.isCommentNode(o)){if(t.isTokenNode(o)){const t=o.value;if(-1===a&&i.isTokenIdent(t)&&modifierFromToken(t)){a=s;continue}if(-1===r&&i.isTokenIdent(t)&&!modifierFromToken(t)){r=s;continue}if(-1===n&&i.isTokenIdent(t)&&d.test(t[4].value)){n=s;if(!1===parseMediaConditionWithoutOr(e.slice(s+1)))return!1;break}return!1}return!1}}let s=[],o=[];-1!==a?(s=e.slice(0,a+1).flatMap((e=>e.tokens())),-1!==r&&(o=e.slice(a+1,r+1).flatMap((e=>e.tokens())))):-1!==r&&(o=e.slice(0,r+1).flatMap((e=>e.tokens())));const u=parseMediaConditionWithoutOr(e.slice(Math.max(a,r,n)+1));return!1===u?new MediaQueryWithType(s,[...o,...e.slice(r+1).flatMap((e=>e.tokens()))]):new MediaQueryWithType(s,o,e.slice(r+1,n+1).flatMap((e=>e.tokens())),u)}}function parseMediaConditionListWithOr(e){let i=!1;const a=[];let r=-1,n=-1;for(let s=0;s<e.length;s++){if(i){const t=parseMediaOr(e.slice(s));if(!1!==t){s+=t.advance,a.push(t.node),n=s;continue}}const o=e[s];if(o.type!==t.ComponentValueType.Whitespace&&o.type!==t.ComponentValueType.Comment){if(i)return!1;if(!1!==i||!t.isSimpleBlockNode(o))return!1;if(o.normalize(),i=parseMediaInParensFromSimpleBlock(o),!1===i)return!1;r=s}}return!(!i||!a.length)&&new MediaConditionListWithOr(i,a,e.slice(0,r).flatMap((e=>e.tokens())),e.slice(n+1).flatMap((e=>e.tokens())))}function parseMediaConditionListWithAnd(e){let i=!1;const a=[];let r=-1,n=-1;for(let s=0;s<e.length;s++){if(i){const t=parseMediaAnd(e.slice(s));if(!1!==t){s+=t.advance,a.push(t.node),n=s;continue}}const o=e[s];if(o.type!==t.ComponentValueType.Whitespace&&o.type!==t.ComponentValueType.Comment){if(i)return!1;if(!1!==i||!t.isSimpleBlockNode(o))return!1;if(o.normalize(),i=parseMediaInParensFromSimpleBlock(o),!1===i)return!1;r=s}}return!(!i||!a.length)&&new MediaConditionListWithAnd(i,a,e.slice(0,r).flatMap((e=>e.tokens())),e.slice(n+1).flatMap((e=>e.tokens())))}function parseMediaCondition(e){const t=parseMediaNot(e);if(!1!==t)return new MediaCondition(t);const i=parseMediaConditionListWithAnd(e);if(!1!==i)return new MediaCondition(i);const a=parseMediaConditionListWithOr(e);if(!1!==a)return new MediaCondition(a);const r=parseMediaInParens(e);return!1!==r&&new MediaCondition(r)}function parseMediaConditionWithoutOr(e){const t=parseMediaNot(e);if(!1!==t)return new MediaCondition(t);const i=parseMediaConditionListWithAnd(e);if(!1!==i)return new MediaCondition(i);const a=parseMediaInParens(e);return!1!==a&&new MediaCondition(a)}function parseMediaInParens(e){let a=-1;for(let i=0;i<e.length;i++){const r=e[i];if(r.type!==t.ComponentValueType.Whitespace&&r.type!==t.ComponentValueType.Comment){if(!t.isSimpleBlockNode(r))return!1;if(-1!==a)return!1;a=i}}if(-1===a)return!1;const r=e[a];if(!i.isTokenOpenParen(r.startToken))return!1;r.normalize();const n=[...e.slice(0,a).flatMap((e=>e.tokens())),r.startToken],s=[r.endToken,...e.slice(a+1).flatMap((e=>e.tokens()))],o=parseMediaFeature(r,n,s);if(!1!==o)return new MediaInParens(o);const u=parseMediaCondition(r.value);return!1!==u?new MediaInParens(u,n,s):new MediaInParens(new GeneralEnclosed(r),e.slice(0,a).flatMap((e=>e.tokens())),e.slice(a+1).flatMap((e=>e.tokens())))}function parseMediaInParensFromSimpleBlock(e){if(!i.isTokenOpenParen(e.startToken))return!1;const t=parseMediaFeature(e,[e.startToken],[e.endToken]);if(!1!==t)return new MediaInParens(t);const a=parseMediaCondition(e.value);return!1!==a?new MediaInParens(a,[e.startToken],[e.endToken]):new MediaInParens(new GeneralEnclosed(e))}exports.MediaQueryModifier=void 0,(s=exports.MediaQueryModifier||(exports.MediaQueryModifier={})).Not="not",s.Only="only";const o=/^not$/i;function parseMediaNot(e){let i=!1,a=null;for(let r=0;r<e.length;r++){const n=e[r];if(n.type!==t.ComponentValueType.Whitespace&&n.type!==t.ComponentValueType.Comment){if(isIdent(n)){const e=n.value;if(o.test(e[4].value)){if(i)return!1;i=!0;continue}return!1}if(!i||!t.isSimpleBlockNode(n))return!1;{n.normalize();const t=parseMediaInParensFromSimpleBlock(n);if(!1===t)return!1;a=new MediaNot(e.slice(0,r).flatMap((e=>e.tokens())),t)}}}return a||!1}const u=/^or$/i;function parseMediaOr(e){let i=!1;for(let a=0;a<e.length;a++){const r=e[a];if(r.type!==t.ComponentValueType.Whitespace&&r.type!==t.ComponentValueType.Comment){if(isIdent(r)){const e=r.value;if(u.test(e[4].value)){if(i)return!1;i=!0;continue}return!1}if(i&&t.isSimpleBlockNode(r)){r.normalize();const t=parseMediaInParensFromSimpleBlock(r);return!1!==t&&{advance:a,node:new MediaOr(e.slice(0,a).flatMap((e=>e.tokens())),t)}}return!1}}return!1}const d=/^and$/i;function parseMediaAnd(e){let i=!1;for(let a=0;a<e.length;a++){const r=e[a];if(r.type!==t.ComponentValueType.Whitespace&&r.type!==t.ComponentValueType.Comment){if(isIdent(r)){const e=r.value;if(d.test(e[4].value)){if(i)return!1;i=!0;continue}return!1}if(i&&t.isSimpleBlockNode(r)){r.normalize();const t=parseMediaInParensFromSimpleBlock(r);return!1!==t&&{advance:a,node:new MediaAnd(e.slice(0,a).flatMap((e=>e.tokens())),t)}}return!1}}return!1}function parseFromTokens(e,i){const a=t.parseCommaSeparatedListOfComponentValues(e,{onParseError:i?.onParseError});return a.map(((e,t)=>{const r=parseMediaQuery(e);return!1===r&&!0===i?.preserveInvalidMediaQueries?new MediaQueryInvalid(a[t]):r})).filter((e=>!!e))}class CustomMedia{type=exports.NodeType.CustomMedia;name;mediaQueryList=null;trueOrFalseKeyword=null;constructor(e,t,i){this.name=e,this.mediaQueryList=t,this.trueOrFalseKeyword=i??null}getName(){for(let e=0;e<this.name.length;e++){const t=this.name[e];if(i.isTokenIdent(t))return t[4].value}return""}getNameToken(){for(let e=0;e<this.name.length;e++){const t=this.name[e];if(i.isTokenIdent(t))return t}return null}hasMediaQueryList(){return!!this.mediaQueryList}hasTrueKeyword(){if(!this.trueOrFalseKeyword)return!1;for(let e=0;e<this.trueOrFalseKeyword.length;e++){const t=this.trueOrFalseKeyword[e];if(!i.isTokenWhiteSpaceOrComment(t))return!!i.isTokenIdent(t)&&"true"===t[4].value.toLowerCase()}return!1}hasFalseKeyword(){if(!this.trueOrFalseKeyword)return!1;for(let e=0;e<this.trueOrFalseKeyword.length;e++){const t=this.trueOrFalseKeyword[e];if(!i.isTokenWhiteSpaceOrComment(t))return!!i.isTokenIdent(t)&&"false"===t[4].value.toLowerCase()}return!1}tokens(){if(this.trueOrFalseKeyword)return[...this.name,...this.trueOrFalseKeyword];if(!this.mediaQueryList)return[...this.name];const e=[];for(let t=0;t<this.mediaQueryList.length;t++){const a=this.mediaQueryList[t];0!==t&&e.push([i.TokenType.Comma,",",-1,-1,void 0]),e.push(...a.tokens())}return[...this.name,...e]}toString(){return i.stringify(...this.tokens())}toJSON(){return{type:this.type,string:this.toString(),nameValue:this.getName(),name:this.name,hasFalseKeyword:this.hasFalseKeyword(),hasTrueKeyword:this.hasTrueKeyword(),trueOrFalseKeyword:this.trueOrFalseKeyword,mediaQueryList:this.mediaQueryList?.map((e=>e.toJSON()))}}isCustomMedia(){return CustomMedia.isCustomMedia(this)}static isCustomMedia(e){return!!e&&(e instanceof CustomMedia&&e.type===exports.NodeType.CustomMedia)}}function parseCustomMediaFromTokens(e,t){let a=[],r=e;for(let t=0;t<e.length;t++)if(!i.isTokenWhiteSpaceOrComment(e[t])){if(i.isTokenIdent(e[t])){if(e[t][4].value.startsWith("--")){a=e.slice(0,t+1),r=e.slice(t+1);break}}return!1}let n=!0;for(let e=0;e<r.length;e++)if(!i.isTokenWhiteSpaceOrComment(r[e])){if(i.isTokenIdent(r[e])){const t=r[e][4].value.toLowerCase();if("false"===t)continue;if("true"===t)continue}if(i.isTokenEOF(r[e]))break;n=!1}return n?new CustomMedia(a,null,r):new CustomMedia(a,parseFromTokens(i.cloneTokens(r),t))}function isMediaConditionListWithAnd(e){return MediaConditionListWithAnd.isMediaConditionListWithAnd(e)}function isMediaConditionListWithOr(e){return MediaConditionListWithOr.isMediaConditionListWithOr(e)}function isMediaFeatureRangeNameValue(e){return MediaFeatureRangeNameValue.isMediaFeatureRangeNameValue(e)}function isMediaFeatureRangeValueName(e){return MediaFeatureRangeValueName.isMediaFeatureRangeValueName(e)}function isMediaFeatureRangeValueNameValue(e){return MediaFeatureRangeValueNameValue.isMediaFeatureRangeValueNameValue(e)}function isMediaQueryWithType(e){return MediaQueryWithType.isMediaQueryWithType(e)}function isMediaQueryWithoutType(e){return MediaQueryWithoutType.isMediaQueryWithoutType(e)}function isMediaQueryInvalid(e){return MediaQueryInvalid.isMediaQueryInvalid(e)}var l;exports.MediaType=void 0,(l=exports.MediaType||(exports.MediaType={})).All="all",l.Print="print",l.Screen="screen",l.Tty="tty",l.Tv="tv",l.Projection="projection",l.Handheld="handheld",l.Braille="braille",l.Embossed="embossed",l.Aural="aural",l.Speech="speech",exports.CustomMedia=CustomMedia,exports.GeneralEnclosed=GeneralEnclosed,exports.MediaAnd=MediaAnd,exports.MediaCondition=MediaCondition,exports.MediaConditionListWithAnd=MediaConditionListWithAnd,exports.MediaConditionListWithOr=MediaConditionListWithOr,exports.MediaFeature=MediaFeature,exports.MediaFeatureBoolean=MediaFeatureBoolean,exports.MediaFeatureName=MediaFeatureName,exports.MediaFeaturePlain=MediaFeaturePlain,exports.MediaFeatureRangeNameValue=MediaFeatureRangeNameValue,exports.MediaFeatureRangeValueName=MediaFeatureRangeValueName,exports.MediaFeatureRangeValueNameValue=MediaFeatureRangeValueNameValue,exports.MediaFeatureValue=MediaFeatureValue,exports.MediaInParens=MediaInParens,exports.MediaNot=MediaNot,exports.MediaOr=MediaOr,exports.MediaQueryInvalid=MediaQueryInvalid,exports.MediaQueryWithType=MediaQueryWithType,exports.MediaQueryWithoutType=MediaQueryWithoutType,exports.cloneMediaQuery=function cloneMediaQuery(e){const t=i.cloneTokens(e.tokens()),a=parseFromTokens(t,{preserveInvalidMediaQueries:!0})[0];if(!a)throw new Error(`Failed to clone media query for : "${i.stringify(...t)}"`);if(isMediaQueryInvalid(e)&&isMediaQueryInvalid(a))return a;if(isMediaQueryWithType(e)&&isMediaQueryWithType(a))return a;if(isMediaQueryWithoutType(e)&&isMediaQueryWithoutType(a))return a;throw new Error(`Failed to clone media query for : "${i.stringify(...t)}"`)},exports.comparisonFromTokens=comparisonFromTokens,exports.invertComparison=function invertComparison(e){switch(e){case exports.MediaFeatureEQ.EQ:return exports.MediaFeatureEQ.EQ;case exports.MediaFeatureLT.LT:return exports.MediaFeatureGT.GT;case exports.MediaFeatureLT.LT_OR_EQ:return exports.MediaFeatureGT.GT_OR_EQ;case exports.MediaFeatureGT.GT:return exports.MediaFeatureLT.LT;case exports.MediaFeatureGT.GT_OR_EQ:return exports.MediaFeatureLT.LT_OR_EQ;default:return!1}},exports.isCustomMedia=function isCustomMedia(e){return CustomMedia.isCustomMedia(e)},exports.isGeneralEnclosed=function isGeneralEnclosed(e){return GeneralEnclosed.isGeneralEnclosed(e)},exports.isMediaAnd=function isMediaAnd(e){return MediaAnd.isMediaAnd(e)},exports.isMediaCondition=function isMediaCondition(e){return MediaCondition.isMediaCondition(e)},exports.isMediaConditionList=function isMediaConditionList(e){return isMediaConditionListWithAnd(e)||isMediaConditionListWithOr(e)},exports.isMediaConditionListWithAnd=isMediaConditionListWithAnd,exports.isMediaConditionListWithOr=isMediaConditionListWithOr,exports.isMediaFeature=function isMediaFeature(e){return MediaFeature.isMediaFeature(e)},exports.isMediaFeatureBoolean=function isMediaFeatureBoolean(e){return MediaFeatureBoolean.isMediaFeatureBoolean(e)},exports.isMediaFeatureName=function isMediaFeatureName(e){return MediaFeatureName.isMediaFeatureName(e)},exports.isMediaFeaturePlain=function isMediaFeaturePlain(e){return MediaFeaturePlain.isMediaFeaturePlain(e)},exports.isMediaFeatureRange=function isMediaFeatureRange(e){return isMediaFeatureRangeNameValue(e)||isMediaFeatureRangeValueName(e)||isMediaFeatureRangeValueNameValue(e)},exports.isMediaFeatureRangeNameValue=isMediaFeatureRangeNameValue,exports.isMediaFeatureRangeValueName=isMediaFeatureRangeValueName,exports.isMediaFeatureRangeValueNameValue=isMediaFeatureRangeValueNameValue,exports.isMediaFeatureValue=function isMediaFeatureValue(e){return MediaFeatureValue.isMediaFeatureValue(e)},exports.isMediaInParens=function isMediaInParens(e){return MediaInParens.isMediaInParens(e)},exports.isMediaNot=function isMediaNot(e){return MediaNot.isMediaNot(e)},exports.isMediaOr=function isMediaOr(e){return MediaOr.isMediaOr(e)},exports.isMediaQuery=function isMediaQuery(e){return isMediaQueryWithType(e)||isMediaQueryWithoutType(e)||isMediaQueryInvalid(e)},exports.isMediaQueryInvalid=isMediaQueryInvalid,exports.isMediaQueryWithType=isMediaQueryWithType,exports.isMediaQueryWithoutType=isMediaQueryWithoutType,exports.matchesComparison=matchesComparison,exports.matchesRatio=matchesRatio,exports.matchesRatioExactly=matchesRatioExactly,exports.modifierFromToken=modifierFromToken,exports.newMediaFeatureBoolean=function newMediaFeatureBoolean(e){const a=[i.TokenType.Ident,"",-1,-1,{value:""}];return i.mutateIdent(a,e),new MediaFeature(new MediaFeatureBoolean(new MediaFeatureName(new t.TokenNode(a))),[[i.TokenType.OpenParen,"(",-1,-1,void 0]],[[i.TokenType.CloseParen,")",-1,-1,void 0]])},exports.newMediaFeaturePlain=function newMediaFeaturePlain(e,...a){const r=[i.TokenType.Ident,"",-1,-1,{value:""}];i.mutateIdent(r,e);const n=t.parseListOfComponentValues(a);return new MediaFeature(new MediaFeaturePlain(new MediaFeatureName(new t.TokenNode(r)),[i.TokenType.Colon,":",-1,-1,void 0],new MediaFeatureValue(1===n.length?n[0]:n)),[[i.TokenType.OpenParen,"(",-1,-1,void 0]],[[i.TokenType.CloseParen,")",-1,-1,void 0]])},exports.parse=function parse(e,t){const a=i.tokenizer({css:e},{onParseError:t?.onParseError}),r=[];for(;!a.endOfFile();)r.push(a.nextToken());return r.push(a.nextToken()),parseFromTokens(r,t)},exports.parseCustomMedia=function parseCustomMedia(e,t){const a=i.tokenizer({css:e},{onParseError:t?.onParseError}),r=[];for(;!a.endOfFile();)r.push(a.nextToken());return r.push(a.nextToken()),parseCustomMediaFromTokens(r,t)},exports.parseCustomMediaFromTokens=parseCustomMediaFromTokens,exports.parseFromTokens=parseFromTokens,exports.typeFromToken=function typeFromToken(e){if(!i.isTokenIdent(e))return!1;switch(e[4].value.toLowerCase()){case exports.MediaType.All:return exports.MediaType.All;case exports.MediaType.Print:return exports.MediaType.Print;case exports.MediaType.Screen:return exports.MediaType.Screen;case exports.MediaType.Tty:return exports.MediaType.Tty;case exports.MediaType.Tv:return exports.MediaType.Tv;case exports.MediaType.Projection:return exports.MediaType.Projection;case exports.MediaType.Handheld:return exports.MediaType.Handheld;case exports.MediaType.Braille:return exports.MediaType.Braille;case exports.MediaType.Embossed:return exports.MediaType.Embossed;case exports.MediaType.Aural:return exports.MediaType.Aural;case exports.MediaType.Speech:return exports.MediaType.Speech;default:return!1}};
