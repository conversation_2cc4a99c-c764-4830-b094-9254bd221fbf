<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * database code for changing schema
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
function xmldb_enrol_stripepaymentpro_upgrade($oldversion) {
    global $DB;

    $dbman = $DB->get_manager();

    // Debug logging
    error_log("STRIPEPAYMENTPRO UPGRADE: Starting upgrade from version $oldversion");

    if ($oldversion < **********) {
        error_log("STRIPEPAYMENTPRO UPGRADE: Processing ********** upgrade block");

        // Define table enrol_stripepro_coupons to be created.
        $table = new xmldb_table('enrol_stripepro_coupons');
        error_log("STRIPEPAYMENTPRO UPGRADE: Creating enrol_stripepro_coupons table");

        // Adding fields to table enrol_stripepro_coupons.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('couponid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('couponname', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('amount_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('percent_off', XMLDB_TYPE_FLOAT, '20,2', null, false);
        $table->add_field('currency', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('duration', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('no_of_months', XMLDB_TYPE_INTEGER, '10', null, false);
        $table->add_field('stripe_product_id', XMLDB_TYPE_CHAR, '255', null, false);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);
        $table->add_field('coupon_expiry', XMLDB_TYPE_INTEGER, '20', null, XMLDB_NOTNULL, null, null, null, 0);

        // Adding keys to table enrol_stripepro_coupons.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('unique_coupon_course', XMLDB_KEY_UNIQUE, ['couponid']);

        // Conditionally launch create table for enrol_stripepro_coupons.
        if (!$dbman->table_exists($table)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Table enrol_stripepro_coupons does not exist, creating it");
            try {
                $dbman->create_table($table);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully created enrol_stripepro_coupons table");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error creating table: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: Table enrol_stripepro_coupons already exists");
        }

        // Stripe savepoint reached.
        error_log("STRIPEPAYMENTPRO UPGRADE: Reaching savepoint **********");
        upgrade_plugin_savepoint(true, **********, 'enrol', 'stripepaymentpro');
        error_log("STRIPEPAYMENTPRO UPGRADE: Successfully completed ********** upgrade block");
    }

    if ($oldversion < 2025073001) {
        error_log("STRIPEPAYMENTPRO UPGRADE: Processing 2025073001 upgrade block");

        // Define table enrol_stripepaymentpro to be checked.
        $table = new xmldb_table('enrol_stripepaymentpro');
        error_log("STRIPEPAYMENTPRO UPGRADE: Working with enrol_stripepaymentpro table");

        // Remove legacy fields that are not used by Stripe payment processing.
        error_log("STRIPEPAYMENTPRO UPGRADE: Starting to drop legacy fields");

        $field = new xmldb_field('business');
        if ($dbman->field_exists($table, $field)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Dropping business field");
            try {
                $dbman->drop_field($table, $field);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully dropped business field");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error dropping business field: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: business field does not exist");
        }

        $fieldsToDrops = ['option_name1', 'option_selection1_x', 'option_name2', 'option_selection2_x', 'parent_txn_id', 'payment_type'];

        foreach ($fieldsToDrops as $fieldName) {
            $field = new xmldb_field($fieldName);
            if ($dbman->field_exists($table, $field)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Dropping field $fieldName");
                try {
                    $dbman->drop_field($table, $field);
                    error_log("STRIPEPAYMENTPRO UPGRADE: Successfully dropped field $fieldName");
                } catch (Exception $e) {
                    error_log("STRIPEPAYMENTPRO UPGRADE: Error dropping field $fieldName: " . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Field $fieldName does not exist");
            }
        }

        // Add a new 'currency' field to the enrol_stripepaymentpro table.
        error_log("STRIPEPAYMENTPRO UPGRADE: Starting to add new fields");

        $field = new xmldb_field('currency', XMLDB_TYPE_CHAR, '10', null, false, false, null, 'memo');
        if (!$dbman->field_exists($table, $field)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Adding currency field");
            try {
                $dbman->add_field($table, $field);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully added currency field");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error adding currency field: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: currency field already exists");
        }

        // Add a new 'stripeemail' field to the enrol_stripepaymentpro table.
        // Note: The reference field might be 'coupon_id' or 'couponid' depending on upgrade state
        $field = new xmldb_field('stripeemail', XMLDB_TYPE_CHAR, '255', null, false, false);
        if (!$dbman->field_exists($table, $field)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Adding stripeemail field");
            try {
                $dbman->add_field($table, $field);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully added stripeemail field");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error adding stripeemail field: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: stripeemail field already exists");
        }

        // Add a new 'receipturl' field to the enrol_stripepaymentpro table.
        $field = new xmldb_field('receipturl', XMLDB_TYPE_CHAR, '500', null, false, false, null, 'trialperiodend');
        if (!$dbman->field_exists($table, $field)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Adding receipturl field");
            try {
                $dbman->add_field($table, $field);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully added receipturl field");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error adding receipturl field: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: receipturl field already exists");
        }

        // Rename existing fields to remove underscores.
        error_log("STRIPEPAYMENTPRO UPGRADE: Starting field rename operations");

        $fieldsToRename = [
            'receiver_email' => 'receiveremail',
            'receiver_id' => 'receiverid',
            'item_name' => 'itemname',
            'coupon_id' => 'couponid'
        ];

        foreach ($fieldsToRename as $oldName => $newName) {
            $field = new xmldb_field($oldName, XMLDB_TYPE_CHAR, '255', null, false, false);
            if ($dbman->field_exists($table, $field)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Renaming field $oldName to $newName");
                try {
                    $dbman->rename_field($table, $field, $newName);
                    error_log("STRIPEPAYMENTPRO UPGRADE: Successfully renamed field $oldName to $newName");
                } catch (Exception $e) {
                    error_log("STRIPEPAYMENTPRO UPGRADE: Error renaming field $oldName to $newName: " . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Field $oldName does not exist (may already be renamed)");
            }
        }

        $moreFieldsToRename = [
            'payment_status' => 'paymentstatus',
            'pending_reason' => 'pendingreason',
            'txn_id' => 'txnid',
            'product_id' => 'productid',
            'product_name' => 'productname'
        ];

        foreach ($moreFieldsToRename as $oldName => $newName) {
            $field = new xmldb_field($oldName, XMLDB_TYPE_CHAR, '255', null, false, false);
            if ($dbman->field_exists($table, $field)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Renaming field $oldName to $newName");
                try {
                    $dbman->rename_field($table, $field, $newName);
                    error_log("STRIPEPAYMENTPRO UPGRADE: Successfully renamed field $oldName to $newName");
                } catch (Exception $e) {
                    error_log("STRIPEPAYMENTPRO UPGRADE: Error renaming field $oldName to $newName: " . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Field $oldName does not exist (may already be renamed)");
            }
        }

        $finalFieldsToRename = [
            'product_type' => 'producttype',
            'subscription_id' => 'subscriptionid',
            'renewal_interval' => 'renewalinterval',
            'renewal_intervalperiod' => 'renewalintervalperiod'
        ];

        foreach ($finalFieldsToRename as $oldName => $newName) {
            $field = new xmldb_field($oldName, XMLDB_TYPE_CHAR, '255', null, false, false);
            if ($dbman->field_exists($table, $field)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Renaming field $oldName to $newName");
                try {
                    $dbman->rename_field($table, $field, $newName);
                    error_log("STRIPEPAYMENTPRO UPGRADE: Successfully renamed field $oldName to $newName");
                } catch (Exception $e) {
                    error_log("STRIPEPAYMENTPRO UPGRADE: Error renaming field $oldName to $newName: " . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Field $oldName does not exist (may already be renamed)");
            }
        }

        // Define table enrol_stripepaymentpro_coupons to be checked.
        // It's possible that the old table name was `enrol_stripepayment_coupons`
        // so we need to check and rename it if it exists.
        error_log("STRIPEPAYMENTPRO UPGRADE: Checking for table rename operations");

        $oldtablename = new xmldb_table('enrol_stripepayment_coupons');
        $newtablename = new xmldb_table('enrol_stripepaymentpro_coupons');

        if ($dbman->table_exists($oldtablename)) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Renaming table enrol_stripepayment_coupons to enrol_stripepaymentpro_coupons");
            try {
                $dbman->rename_table($oldtablename, $newtablename);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully renamed table");
            } catch (Exception $e) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Error renaming table: " . $e->getMessage());
                throw $e;
            }
        } else {
            error_log("STRIPEPAYMENTPRO UPGRADE: Old table enrol_stripepayment_coupons does not exist");
        }

        $table = new xmldb_table('enrol_stripepaymentpro_coupons');
        error_log("STRIPEPAYMENTPRO UPGRADE: Working with coupon table field renames");

        $couponFieldsToRename = [
            'coupon_name' => 'couponname',
            'amount_off' => 'amountoff',
            'percent_off' => 'percentoff',
            'no_of_months' => 'noofmonths',
            'stripe_product_id' => 'stripeproductid',
            'coupon_expiry' => 'couponexpiry'
        ];

        foreach ($couponFieldsToRename as $oldName => $newName) {
            $fieldType = ($oldName === 'amount_off' || $oldName === 'percent_off') ? XMLDB_TYPE_FLOAT :
                        (($oldName === 'no_of_months' || $oldName === 'coupon_expiry') ? XMLDB_TYPE_INTEGER : XMLDB_TYPE_CHAR);
            $fieldLength = ($oldName === 'amount_off' || $oldName === 'percent_off') ? '20,2' :
                          (($oldName === 'no_of_months' || $oldName === 'coupon_expiry') ? '10' : '255');

            $field = new xmldb_field($oldName, $fieldType, $fieldLength, null, false, false);
            if ($dbman->field_exists($table, $field)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Renaming coupon field $oldName to $newName");
                try {
                    $dbman->rename_field($table, $field, $newName);
                    error_log("STRIPEPAYMENTPRO UPGRADE: Successfully renamed coupon field $oldName to $newName");
                } catch (Exception $e) {
                    error_log("STRIPEPAYMENTPRO UPGRADE: Error renaming coupon field $oldName to $newName: " . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Coupon field $oldName does not exist (may already be renamed)");
            }
        }

        // Handle key operations for coupon table
        error_log("STRIPEPAYMENTPRO UPGRADE: Starting key operations on coupon table");

        // Drop the old key if it exists.
        try {
            $oldkey = new xmldb_key('unique_coupon_course', XMLDB_KEY_UNIQUE, ['couponid']);
            if ($dbman->find_key_name($table, $oldkey)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Dropping old key unique_coupon_course");
                $dbman->drop_key($table, $oldkey);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully dropped old key");
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: Old key unique_coupon_course does not exist");
            }
        } catch (Exception $e) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Error with old key operation: " . $e->getMessage());
            // Continue anyway, key might not exist
        }

        // Add the new key if it doesn't exist.
        try {
            $newkey = new xmldb_key('unique_couponid', XMLDB_KEY_UNIQUE, ['couponid']);
            if (!$dbman->find_key_name($table, $newkey)) {
                error_log("STRIPEPAYMENTPRO UPGRADE: Adding new key unique_couponid");
                $dbman->add_key($table, $newkey);
                error_log("STRIPEPAYMENTPRO UPGRADE: Successfully added new key");
            } else {
                error_log("STRIPEPAYMENTPRO UPGRADE: New key unique_couponid already exists");
            }
        } catch (Exception $e) {
            error_log("STRIPEPAYMENTPRO UPGRADE: Error adding new key: " . $e->getMessage());
            // Continue anyway, key might already exist
        }

        // Stripe savepoint reached.
        error_log("STRIPEPAYMENTPRO UPGRADE: Reaching savepoint 2025073001");
        upgrade_plugin_savepoint(true, 2025073001, 'enrol', 'stripepaymentpro');
        error_log("STRIPEPAYMENTPRO UPGRADE: Successfully completed 2025073001 upgrade block");
    }

    error_log("STRIPEPAYMENTPRO UPGRADE: Upgrade completed successfully");
    return true;
}