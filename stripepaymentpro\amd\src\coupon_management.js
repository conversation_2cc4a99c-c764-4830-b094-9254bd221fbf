// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Coupon management functionality for stripepaymentpro plugin
 *
 * @module     enrol_stripepaymentpro/coupon_management
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

import ajax from 'core/ajax';
import Str from 'core/str';

let localized = {};

export const init = () => {
    Str.get_strings([
        {key: 'nosearchresults', component: 'enrol_stripepaymentpro'},
        {key: 'copied', component: 'enrol_stripepaymentpro'},
        {key: 'failed', component: 'enrol_stripepaymentpro'},
        {key: 'areyousuredeletecoupon', component: 'enrol_stripepaymentpro'},
        {key: 'areyousuredeleteallcoupons', component: 'enrol_stripepaymentpro'},
        {key: 'errordeletingcoupon', component: 'enrol_stripepaymentpro'},
        {key: 'successmessage', component: 'enrol_stripepaymentpro'}
    ]).then(strings => {
        localized = {
            noResults: strings[0],
            copied: strings[1],
            failed: strings[2],
            confirmDelete: strings[3],
            confirmDeleteAll: strings[4],
            errorDelete: strings[5],
            errorDeleteAll: strings[6],
            success: strings[7],
        };

        const allCouponsButton = document.getElementById('allcouponbutton');
        const generateCouponsButton = document.getElementById('generatecouponsbutton');
        const allCouponsSection = document.getElementById('allcouponssection');
        const generateCouponsSection = document.getElementById('generatecouponsection');

        const showAllCoupons = () => {
            if (allCouponsSection && generateCouponsSection) {
                allCouponsSection.style.display = 'block';
                generateCouponsSection.style.display = 'none';
            }
            if (allCouponsButton) {
                allCouponsButton.classList.remove('btnsecondary');
                allCouponsButton.classList.add('btnprimary', 'active');
            }
            if (generateCouponsButton) {
                generateCouponsButton.classList.remove('btnprimary', 'active');
                generateCouponsButton.classList.add('btnsecondary');
            }
        };

        const showGenerateCoupons = () => {
            if (allCouponsSection && generateCouponsSection) {
                allCouponsSection.style.display = 'none';
                generateCouponsSection.style.display = 'block';
            }
            if (generateCouponsButton) {
                generateCouponsButton.classList.remove('btnsecondary');
                generateCouponsButton.classList.add('btnprimary', 'active');
            }
            if (allCouponsButton) {
                allCouponsButton.classList.remove('btnprimary', 'active');
                allCouponsButton.classList.add('btnsecondary');
            }
        };

        if (allCouponsButton) {
            allCouponsButton.addEventListener('click', showAllCoupons);
        }
        if (generateCouponsButton) {
            generateCouponsButton.addEventListener('click', showGenerateCoupons);
        }

        showAllCoupons();
        initializeSearch();
        initializeCopyFunctionality();
        initializeBulkActions();
        initializeFilters();
    }).catch(error => {
        console.error('Failed to load localized strings:', error);
    });
};

const initializeSearch = () => {
    const searchInput = document.getElementById('couponsearch');
    const clearSearchButton = document.getElementById('clear-search');
    const couponsTable = document.getElementById('coupons-table');

    if (!searchInput || !couponsTable) return;

    const performSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const courseFilter = document.getElementById('course-filter-select');
        const selectedCourse = courseFilter ? courseFilter.value : 'All course';
        const rows = couponsTable.querySelectorAll('tbody tr.coupon-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const courseName = row.getAttribute('datacoursename')?.toLowerCase() || '';
            const couponName = row.getAttribute('datacouponname')?.toLowerCase() || '';
            const discountText = row.getAttribute('datadiscounttext')?.toLowerCase() || '';

            // Check search term match
            const searchMatches = !searchTerm || courseName.includes(searchTerm) || couponName.includes(searchTerm) || discountText.includes(searchTerm);

            // Check course filter match
            const courseMatches = selectedCourse === 'All course' || row.getAttribute('datacoursename') === selectedCourse;

            const shouldShow = searchMatches && courseMatches;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        updateNoResultsMessage(visibleCount, searchTerm);
    };

    const updateNoResultsMessage = (visibleCount, searchTerm) => {
        let noResultsRow = couponsTable.querySelector('.noresultsrow');

        if (visibleCount === 0 && searchTerm !== '') {
            if (!noResultsRow) {
                noResultsRow = document.createElement('tr');
                noResultsRow.className = 'noresultsrow';
                noResultsRow.innerHTML = `<td colspan="4" class="text-center text-muted py-4">
                    <i class="fa fa-search mr-2"></i>${localized.noResults}
                </td>`;
                couponsTable.querySelector('tbody').appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else if (noResultsRow) {
            noResultsRow.style.display = 'none';
        }
    };

    const clearSearch = () => {
        searchInput.value = '';
        performSearch();
        searchInput.focus();
    };

    searchInput.addEventListener('input', performSearch);
    searchInput.addEventListener('keyup', e => { if (e.key === 'Escape') clearSearch(); });
    if (clearSearchButton) clearSearchButton.addEventListener('click', clearSearch);
};

const initializeCopyFunctionality = () => {
    document.addEventListener('click', event => {
        const button = event.target.closest('.copycouponname');
        if (!button) return;

        const couponId = button.getAttribute('datacouponname');
        if (!couponId) return;

        const tempTextarea = document.createElement('textarea');
        tempTextarea.value = couponId;
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        tempTextarea.setSelectionRange(0, 99999);

        const originalText = button.innerHTML;
        try {
            document.execCommand('copy');
            button.innerHTML = `<i class="fa fa-check ml-1" style="font-size: 0.8em; color: green;"></i> ${localized.copied}`;
            button.style.color = 'green';
        } catch (err) {
            button.innerHTML = `<i class="fa fa-times ml-1" style="font-size: 0.8em; color: red;"></i> ${localized.failed}`;
            button.style.color = 'red';
        } finally {
            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.color = '';
            }, 2000);
            document.body.removeChild(tempTextarea);
        }
    });
};

export const handleCouponDelete = async (courseId, couponId, button) => {
    if (!confirm(localized.confirmDelete)) return;

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_coupon',
            args: { courseid: courseId, couponid: couponId }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            button.closest('tr')?.remove();
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            alert(`${localized.errorDelete}: ${result.message || 'Unknown error'}`);
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash"></i>';
        button.disabled = false;
        alert(`${localized.errorDelete}: ${error.message}`);
    }
};

export const handleDeleteAllCoupons = async (courseId, button) => {
    if (!confirm(localized.confirmDeleteAll)) return;

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        const request = {
            methodname: 'moodle_stripepaymentpro_deactivate_all_coupons',
            args: { courseid: courseId }
        };

        const result = await ajax.call([request])[0];

        if (result.success) {
            alert(`${localized.success}: ${result.message}`);
            window.location.reload();
        } else {
            button.innerHTML = originalHTML;
            button.disabled = false;
            alert(`${localized.errorDeleteAll}: ${result.message || 'Unknown error'}`);
        }
    } catch (error) {
        button.innerHTML = '<i class="fa fa-trash-alt"></i>';
        button.disabled = false;
        alert(`${localized.errorDeleteAll}: ${error.message}`);
    }
};

const initializeBulkActions = () => {
    const bulkActionSelect = document.getElementById('bulk-action-select');
    const applyButton = document.getElementById('apply-bulk-action');
    const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
    const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');

    if (!bulkActionSelect || !applyButton) return;

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Handle individual checkboxes
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedBoxes.length === rowCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < rowCheckboxes.length;
            }
        });
    });

    // Handle apply button
    applyButton.addEventListener('click', async function(e) {
        e.preventDefault();
        const selectedAction = bulkActionSelect.value;
        const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');

        if (selectedAction === '1' && checkedBoxes.length > 0) { // Delete action
            if (!confirm(`Are you sure you want to delete ${checkedBoxes.length} selected coupon(s)?`)) {
                return;
            }

            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Processing...';
            this.disabled = true;

            try {
                const deletePromises = Array.from(checkedBoxes).map(checkbox => {
                    const row = checkbox.closest('tr');
                    const courseId = row.getAttribute('data-course-id');
                    const couponId = row.querySelector('.deactivate-coupon-btn').getAttribute('data-coupon-id');

                    return ajax.call([{
                        methodname: 'moodle_stripepaymentpro_deactivate_coupon',
                        args: { courseid: courseId, couponid: couponId }
                    }])[0];
                });

                const results = await Promise.all(deletePromises);
                let successCount = 0;
                let errorCount = 0;

                results.forEach((result, index) => {
                    if (result.success) {
                        checkedBoxes[index].closest('tr').remove();
                        successCount++;
                    } else {
                        errorCount++;
                    }
                });

                if (successCount > 0) {
                    alert(`Successfully deleted ${successCount} coupon(s).`);
                }
                if (errorCount > 0) {
                    alert(`Failed to delete ${errorCount} coupon(s).`);
                }

                // Reset checkboxes
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                }

            } catch (error) {
                alert(`Error during bulk delete: ${error.message}`);
            } finally {
                this.innerHTML = originalText;
                this.disabled = false;
            }
        }
    });
};

const initializeFilters = () => {
    const courseFilter = document.getElementById('course-filter-select');
    const couponsTable = document.getElementById('coupons-table');

    if (!courseFilter || !couponsTable) return;

    // Populate course filter with actual courses
    const courses = new Set();
    const rows = couponsTable.querySelectorAll('tbody tr.coupon-row');

    rows.forEach(row => {
        const courseName = row.getAttribute('datacoursename');
        if (courseName) {
            courses.add(courseName);
        }
    });

    // Clear existing options except the first one
    while (courseFilter.children.length > 1) {
        courseFilter.removeChild(courseFilter.lastChild);
    }

    // Add course options
    courses.forEach(courseName => {
        const option = document.createElement('option');
        option.value = courseName;
        option.textContent = courseName;
        courseFilter.appendChild(option);
    });

    // Handle course filter change
    courseFilter.addEventListener('change', function() {
        const selectedCourse = this.value;

        rows.forEach(row => {
            const courseName = row.getAttribute('datacoursename');
            const shouldShow = selectedCourse === 'All course' || courseName === selectedCourse;
            row.style.display = shouldShow ? '' : 'none';
        });

        // Update search results if there's an active search
        const searchInput = document.getElementById('couponsearch');
        if (searchInput && searchInput.value.trim()) {
            // Trigger search to update results with new filter
            searchInput.dispatchEvent(new Event('input'));
        }
    });
};

window.handleCouponDelete = handleCouponDelete;
window.handleDeleteAllCoupons = handleDeleteAllCoupons;