{"name": "@rollup/plugin-commonjs", "version": "28.0.6", "publishConfig": {"access": "public"}, "description": "Convert CommonJS modules to ES2015", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/commonjs"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/commonjs/#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=16.0.0 || 14 >= 14.17"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "npm", "modules", "commonjs", "require"], "peerDependencies": {"rollup": "^2.68.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "dependencies": {"@rollup/pluginutils": "^5.0.1", "commondir": "^1.0.1", "estree-walker": "^2.0.2", "fdir": "^6.2.0", "is-reference": "1.2.1", "magic-string": "^0.30.3", "picomatch": "^4.0.2"}, "devDependencies": {"@rollup/plugin-json": "^5.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "locate-character": "^2.0.5", "require-relative": "^0.8.7", "rollup": "^4.0.0-24", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "typescript": "^4.8.3"}, "types": "./types/index.d.ts", "ava": {"workerThreads": false, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose && pnpm test:ts", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm --workspace-root package:release $(pwd)", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}}