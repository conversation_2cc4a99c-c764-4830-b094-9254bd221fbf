{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/coupon_management

    Template for coupon management interface

    Context variables required for this template:
    * coupons - Array of coupon data grouped by course
    * hascoupons - Boolean indicating if there are any coupons

    Example context (json):
    {
        "coupondata": [
            {
                "coursename": "Course Name",
                "courseid": "prod_123",
                "hasmultiplecoupons": true,
                "coupondata": [
                    {
                        "couponid": "coupon_123",
                        "couponname": "DISCOUNT10",
                        "discounttext": "10% off",
                        "durationtext": " forever",
                        "expirytext": ". Expiry: Never",
                        "fulldescription": "10% off forever. Expiry: Never",
                        "creationdate": Y-m-d H:i:s",
                    }
                ]
            }
        ],
        "hascoupons": true
    }
}}

<!-- Navigation tabs -->
<nav class='strip-coupon-navigation-section' style='margin-bottom: 20px;'>
    <button id="allcouponbutton" style='margin-right: 10px;'>{{#str}}allcoupons, enrol_stripepaymentpro{{/str}}</button>
    <button id="generatecouponsbutton">{{#str}}generatecoupons, enrol_stripepaymentpro{{/str}}</button>
</nav>

<!-- Display table of courses and associated coupons -->
<section class='all-coupons-section' id='allcouponssection' style='display: block;'>
    {{#hascoupons}}
    <!-- Search functionality -->
    <div class="search-container mb-3">             
        <div class="row">
            <div class="col-md-6">
                <form class="form-inline">
                    <select class="form-select mr-sm-2" aria-label="Default select example">
                        <option selected>Bulk Action</option>
                        <option value="1">Delect</option>
                    </select>
                    <select class="form-select mr-sm-2" aria-label="Default select example">
                        <option selected>All course</option>
                        <option value="1">course One</option>
                        <option value="2">course Two</option>
                        <option value="3">course Three</option>
                    </select>
                    <button type="submit" class="btn btn-primary">Apply</button>
                </form>
            </div>
             <div class="col-md-6 d-flex justify-content-end">
                <form class="form-inline">
                     <input type="search"
                           id="couponsearch"
                           class="form-control"
                           placeholder="{{#str}}search_coupons_placeholder, enrol_stripepaymentpro{{/str}}"
                           aria-label="{{#str}}search_coupons, enrol_stripepaymentpro{{/str}}">
                </form>
            </div>
        </div>
    </div>


    <div class="table-responsive">
        <table class="table table-striped" id="coupons-table">
            <thead>
                <tr>
                    <th><input type="checkbox"></th>
                    <th>{{#str}}couponname, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}discountamount, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}coursename, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}actions, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#coupons}}
                {{#coupons}}
                <tr class="coupon-row"
                    datacoursename="{{coursename}}"
                    datacouponname="{{couponname}}"
                    datadiscounttext="{{discounttext}}"
                    data-course-id="{{courseid}}">
                    <td><input type="checkbox"></td>
                    <td class="coupon-name-cell">
                        <button class="copycouponname btn btn-sm btn-outline-secondary"
                                datacouponname="{{couponname}}"
                                title="{{#str}}click_to_copy_coupon, enrol_stripepaymentpro{{/str}}"
                                style="font-family: monospace; padding: 4px 8px;">
                            {{couponname}}
                            <i class="fa fa-copy ml-1" style="font-size: 0.8em;"></i>
                        </button>
                    </td>
                    <td class="discount-cell">
                        <span class="badge badge-info">{{discounttext}}</span>
                        <span class="badge badge-secondary">{{durationtext}}</span>
                        {{#expirytext}}
                        <br><small class="text-muted">{{expirytext}}</small>
                        <small class="text-muted"> Created - {{creationdate}}</small>
                        {{/expirytext}}
                    </td>
                    <td class="course-name-cell">
                        <div class="course-info">
                        {{#isfirstcoupon}}
                            <div class="course-name-wrapper">
                                <i class="fa fa-graduation-cap mr-2"></i>
                                <span class="course-name">{{coursename}}</span>
                            </div>
                            <div class="course-actions mt-2">
                                <div class="dropdown">
                                    <button class="btn btn-sm btnsecondary dropdown-toggle"
                                            type="button"
                                            id="dropdownMenuButton-{{courseid}}"
                                            data-toggle="dropdown"
                                            data-bs-toggle="dropdown"
                                            aria-haspopup="true"
                                            aria-expanded="false"
                                            style="font-size: 0.7em;">
                                        <i class="fa fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton-{{courseid}}">
                                        <button class="dropdown-item deactivate-all-btn"
                                                data-course-id="{{courseid}}"
                                                onclick="handleDeleteAllCoupons('{{courseid}}', this)">
                                            <i class="fa fa-trash-alt mr-2"></i>{{#str}}deleteallcoupons, enrol_stripepaymentpro{{/str}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{/isfirstcoupon}}
                    </td>
                    <td class="actions-cell">
                        <button class="btn btn-sm btn-outline-danger deactivate-coupon-btn"
                                data-course-id="{{courseid}}"
                                data-coupon-id="{{couponid}}"
                                title="{{#str}}deactivatecoupon, enrol_stripepaymentpro{{/str}}"
                                onclick="handleCouponDelete('{{courseid}}', '{{couponid}}', this)">
                            <i class="fa fa-trash"></i>
                        </button>   
                        <div class="action-btn">
                            <i class="bi bi-three-dots-vertical"></i>
                            <div class="action-drop-down">
                                <ul>
                                    <li onclick="handleDeleteAllCoupons('{{courseid}}', this)">
                                        <i class="fa fa-trash-alt mr-2"></i>{{#str}}deleteallcoupons, enrol_stripepaymentpro{{/str}}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
                {{/coupons}}
                {{/coupons}}
            </tbody>
        </table>
    </div>
    {{/hascoupons}}
    {{^hascoupons}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i>
        {{#str}}nocouponfound, enrol_stripepaymentpro{{/str}}
    </div>
    {{/hascoupons}}
</section>

<!-- Form section for generating coupons -->
<section class='generate-coupon-section' id='generatecouponsection' style='display: none;'>
    <h3>{{#str}}createnewcoupon, enrol_stripepaymentpro{{/str}}</h3>
    <div id="coupon-form-container">
        <!-- Form will be rendered by PHP and inserted here -->
    </div>
</section>

<script>
    // Initialize Bootstrap dropdowns for three-dot buttons (support both Bootstrap 4 and 5)
    const dropdownButtons = document.querySelectorAll('[data-toggle="dropdown"], [data-bs-toggle="dropdown"]');
    dropdownButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                if (menu !== this.nextElementSibling) {
                    menu.classList.remove('show');
                    menu.parentElement.querySelector('.dropdown-toggle').setAttribute('aria-expanded', 'false');
                }
            });
            
            // Toggle current dropdown
            const dropdownMenu = this.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                const isShowing = dropdownMenu.classList.contains('show');
                dropdownMenu.classList.toggle('show');
                this.setAttribute('aria-expanded', isShowing ? 'false' : 'true');
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
                const toggle = menu.parentElement.querySelector('.dropdown-toggle');
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
</script>
