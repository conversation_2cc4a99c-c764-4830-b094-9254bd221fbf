define(["exports","core/str"],function(e,t){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var c=n(t);let r={};const o=()=>{const e=document.getElementById("select-all-receipts"),t=document.querySelectorAll(".receipt-checkbox"),n=document.getElementById("bulk-action-select"),c=document.getElementById("apply-bulk-action");n&&c&&(e&&e.addEventListener("change",function(){t.forEach(e=>{e.disabled||(e.checked=this.checked)})}),t.forEach(n=>{n.addEventListener("change",function(){const n=Array.from(t).filter(e=>!e.disabled),c=n.filter(e=>e.checked);e&&(e.checked=c.length===n.length,e.indeterminate=c.length>0&&c.length<n.length)})}),c.addEventListener("click",function(){const e=n.value,t=document.querySelectorAll(".receipt-checkbox:checked");if("download"===e&&t.length>0){const e=Array.from(t).map(e=>e.closest("tr").dataset.receiptUrl).filter(e=>e&&""!==e.trim());e.length>0?(e.forEach((e,t)=>{setTimeout(()=>{window.open(e,"_blank")},500*t)}),alert(`Opening ${e.length} receipt(s) for download.`)):alert("No valid receipt URLs found for selected items.")}else 0===t.length&&alert("Please select at least one receipt.")}))},a=()=>{const e=document.getElementById("receipt-search"),t=document.getElementById("clear-search"),n=document.getElementById("receipts-table");if(!e||!n)return;const c=()=>{const t=e.value.toLowerCase(),c=document.getElementById("course-filter-select"),r=document.getElementById("date-filter-select"),a=c?c.value:"All course",l=r?r.value:"all",s=n.querySelectorAll("tbody tr");let d=0;s.forEach(function(e){const n=e.getAttribute("datacoursename")?.toLowerCase()||"",c=e.getAttribute("data-student-name")?.toLowerCase()||"",r=e.getAttribute("data-student-email")?.toLowerCase()||"",o=e.getAttribute("data-payment-date")||"",s=!t||n.includes(t)||c.includes(t)||r.includes(t),i="All course"===a||e.getAttribute("datacoursename")===a,m=u(o,l),p=s&&i&&m;e.style.display=p?"":"none",p&&d++}),o(d,t)},o=(e,t)=>{let c=n.querySelector(".noresultsrow");0===e&&""!==t?(c||(c=document.createElement("tr"),c.className="noresultsrow",c.innerHTML=`<td colspan="6" class="text-center text-muted py-4">\n                    <i class="fa fa-search mr-2"></i>${r.noResults}\n                </td>`,n.querySelector("tbody").appendChild(c)),c.style.display=""):c&&(c.style.display="none")},a=()=>{e.value="",c(),e.focus()};e.addEventListener("input",c),e.addEventListener("keyup",e=>{"Escape"===e.key&&a()}),t&&t.addEventListener("click",a)},l=()=>{document.addEventListener("click",e=>{const t=e.target.closest(".copy-receipt-url");if(!t)return;const n=t.getAttribute("data-receipt-url");if(!n)return;const c=t.innerHTML;navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(n).then(()=>{s(t,c)}).catch(()=>{d(n,t,c)}):d(n,t,c)})},s=(e,t)=>{e.innerHTML=`<i class="fa fa-check text-success"></i> ${r.copied}`,e.classList.add("btn-success"),e.classList.remove("btn-outline-secondary"),setTimeout(()=>{e.innerHTML=t,e.classList.remove("btn-success"),e.classList.add("btn-outline-secondary")},2e3)},d=(e,t,n)=>{const c=document.createElement("textarea");c.value=e,c.style.top="0",c.style.left="0",c.style.position="fixed",document.body.appendChild(c),c.focus(),c.select();try{document.execCommand("copy"),s(t,n)}catch(e){t.innerHTML=`<i class="fa fa-times text-danger"></i> ${r.failed}`,setTimeout(()=>{t.innerHTML=n},2e3)}document.body.removeChild(c)},i=()=>{const e=document.getElementById("course-filter-select"),t=document.getElementById("date-filter-select"),n=document.getElementById("receipts-table");if(!e||!n)return;const c=new Set;for(n.querySelectorAll("tbody tr.receipt-row").forEach(e=>{const t=e.getAttribute("datacoursename");t&&c.add(t)});e.children.length>1;)e.removeChild(e.lastChild);c.forEach(t=>{const n=document.createElement("option");n.value=t,n.textContent=t,e.appendChild(n)});const r=()=>{const e=document.getElementById("receipt-search");e&&e.dispatchEvent(new Event("input"))};e.addEventListener("change",r),t&&t.addEventListener("change",r)},u=(e,t)=>{if("all"===t||!e)return!0;const n=new Date(e),c=new Date;switch(t){case"today":return n.toDateString()===c.toDateString();case"week":return n>=new Date(c.getTime()-6048e5);case"month":return n>=new Date(c.getFullYear(),c.getMonth()-1,c.getDate());case"year":return n>=new Date(c.getFullYear()-1,c.getMonth(),c.getDate());default:return!0}};e.init=()=>{c.default.get_strings([{key:"copied",component:"enrol_stripepaymentpro"},{key:"failed",component:"enrol_stripepaymentpro"},{key:"nosearchresults",component:"enrol_stripepaymentpro"}]).then(e=>{r={copied:e[0],failed:e[1],noResults:e[2]},o(),a(),l(),i()}).catch(e=>{console.error("Failed to load localized strings:",e)})},Object.defineProperty(e,"__esModule",{value:!0})});
