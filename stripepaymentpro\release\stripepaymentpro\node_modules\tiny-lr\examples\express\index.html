<!DOCTYPE HTML>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>WD Tests</title>
  <link rel="stylesheet" href="styles/site.css">
</head>
<body>

  <div class="test-lr">
    Testing livereload thing

    <a href="#" class="fun">fun</a>
  </div>

  <script>document.write('<script src="http://'
    + location.host
    + '/livereload.js?snipver=1"></'
    + 'script>')</script>

  <script>
  (function() {

    window.onload = function() {
      var fun = document.querySelector('.fun');
      var lr = document.querySelector('.test-lr');
      fun.addEventListener('click', function(e) {
        e.preventDefault();
        var d3 = /d3/.test(lr.className) ? '' : 'd3';
        lr.className = 'test-lr ' + d3;
      });
    };
  })();
  </script>


</body>
</html>
