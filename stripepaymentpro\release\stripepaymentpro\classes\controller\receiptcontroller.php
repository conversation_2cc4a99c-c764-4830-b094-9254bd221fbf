<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Receipt controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

/**
 * Receipt controller class
 */
class receiptcontroller {

    /**
     * Get all receipts data
     *
     * @return array Receipt data
     */
    public function getreceiptsdata() {
        global $DB;

        $sql = "SELECT sp.id, sp.courseid, sp.userid, sp.receipturl, sp.timeupdated,
                       c.fullname as coursename, u.firstname, u.lastname, u.email
                FROM {enrol_stripepaymentpro} sp
                JOIN {course} c ON c.id = sp.courseid
                JOIN {user} u ON u.id = sp.userid
                WHERE sp.receipturl IS NOT NULL AND sp.receipturl != ''
                ORDER BY sp.timeupdated DESC";

        return $DB->get_records_sql($sql);
    }

    /**
     * Prepare receipt data for template rendering
     *
     * @param array $receipts_data Raw receipt data
     * @return array Template data
     */
    public function preparereceipttemplatedata($receipts_data) {
        $templatedata = [];

        foreach ($receipts_data as $receipt) {
            $studentname = trim($receipt->firstname . ' ' . $receipt->lastname);
            if (empty($studentname)) {
                $studentname = $receipt->email;
            }

            $templatedata[] = [
                'id' => $receipt->id,
                'coursename' => htmlspecialchars($receipt->coursename),
                'studentname' => htmlspecialchars($studentname),
                'studentemail' => htmlspecialchars($receipt->email),
                'receipturl' => $receipt->receipturl,
                'paymentdate' => userdate($receipt->timeupdated),
                'hasreceipturl' => !empty($receipt->receipturl)
            ];
        }

        return $templatedata;
    }
}
