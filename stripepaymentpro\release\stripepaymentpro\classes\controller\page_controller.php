<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Main page controller for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\controller;

defined('MOODLE_INTERNAL') || die();

use enrol_stripepaymentpro\controller\subscriptioncontroller;
use enrol_stripepaymentpro\controller\couponcontroller;
use enrol_stripepaymentpro\controller\receiptcontroller;
use enrol_stripepaymentpro\controller\enrol_stripepaymentpro_webhook_controller;

/**
 * Main page controller for handling different page requests
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class page_controller {

    /**
     * Handle subscription management page
     * 
     * @param int $userid User ID (optional)
     * @return void
     */
    public static function handlesubscriptionpage($userid = 0) {
        global $CFG, $PAGE, $OUTPUT, $USER;

        require_login();
        
        $PAGE->set_context(\context_system::instance());
        $PAGE->set_title(get_string('mysubscription', 'enrol_stripepaymentpro'));
        $PAGE->set_heading(get_string('mysubscription', 'enrol_stripepaymentpro'));
        $PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php');
        $PAGE->set_pagelayout('standard');

        $isadmin = is_siteadmin($USER->id);
        $subscriptioncontroller = new subscriptioncontroller();

        // Handle subscription cancellation
        $subid = optional_param('subid', null, PARAM_RAW);
        if ($subid !== null) {
            $subscriptiondata = $subscriptioncontroller->getsubscriptions($userid, $isadmin);
            $allowedsubscriptions = array_column($subscriptiondata['stripesubscriptions'], 'id');
            
            if ($subscriptioncontroller->cancelsubscription($subid, $allowedsubscriptions)) {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php',
                    get_string('cancelsuccess', 'enrol_stripepaymentpro'));
            } else {
                redirect($CFG->wwwroot . '/enrol/stripepaymentpro/mysubscription.php',
                    get_string('cancelfailed', 'enrol_stripepaymentpro'));
            }
        }

        // Get subscription data
        $subscriptiondata = $subscriptioncontroller->getsubscriptions($userid, $isadmin);
        $templatedata = $subscriptioncontroller->preparetemplatedata(
            $subscriptiondata['stripesubscriptions'],
            $subscriptiondata['subscriptionsbyid'],
            $isadmin
        );

        // Page header
        echo $OUTPUT->header();

        // Render template
        echo $OUTPUT->render_from_template('enrol_stripepaymentpro/mysubscription', $templatedata);

        // Page footer
        echo $OUTPUT->footer();
    }

    /**
     * Handle coupon management page
     * 
     * @return void
     */
    public static function handlecouponpage() {
        global $CFG, $PAGE, $OUTPUT, $USER;

        require_login();

        // Check if the user is an admin
        if (!is_siteadmin($USER->id)) {
            redirect($CFG->wwwroot);
        }

        // Set up page
        $PAGE->set_context(\context_system::instance());
        $PAGE->set_title(get_string('couponmanagement', 'enrol_stripepaymentpro'));
        $PAGE->set_heading(get_string('couponmanagement', 'enrol_stripepaymentpro'));
        $PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/coupon_management.php');
        $PAGE->set_pagelayout('admin');

        $couponcontroller = new couponcontroller();
        $return = new \moodle_url('/enrol/stripepaymentpro/coupon_management.php');

        // Get coupon data
        $coupondata = $couponcontroller->get_coupons_data();
        $stripecourselist = $couponcontroller->get_stripe_courses();

        // Handle form submission
        require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/form/coupon_form.php');
        $formactionurl = new \moodle_url('/enrol/stripepaymentpro/coupon_management.php');
        $mform = new \enrol_stripepaymentpro\form\coupon_form($formactionurl, [
            $stripecourselist,
            enrol_get_plugin('stripepaymentpro'),
            enrol_get_plugin('stripepaymentpro')
        ]);

        if ($mform->is_cancelled()) {
            redirect($return);
        } else if ($formdata = $mform->get_data()) {
            if ($couponcontroller->create_coupon($formdata)) {
                \core\notification::success(get_string('couponcreatedsuccess', 'enrol_stripepaymentpro'));
            }
            redirect($return);
        }

        // Prepare template data
        $templatedata = [
            'coupons' => $couponcontroller->preparecoupontemplatedata(
                $coupondata['couponsbycourse'],
                $coupondata['couponslist'],
                $coupondata['courses']
            ),
            'hascoupons' => $coupondata['hascoupons']
        ];

        echo $OUTPUT->header();

        // Render coupon management template
        echo $OUTPUT->render_from_template('enrol_stripepaymentpro/coupon_management', $templatedata);

        // Capture form output
        ob_start();
        $mform->display();
        $form_html = ob_get_clean();

        // Inject form into the generate coupon section using JavaScript
        echo "<script>";
        echo "document.addEventListener('DOMContentLoaded', function() {";
        echo "    const formContainer = document.getElementById('coupon-form-container');";
        echo "    if (formContainer) {";
        echo "        formContainer.innerHTML = " . json_encode($form_html) . ";";
        echo "    }";
        echo "});";
        echo "</script>";

        // Initialize JavaScript modules
        $PAGE->requires->js_call_amd('enrol_stripepaymentpro/coupon_management', 'init', []);
        $PAGE->requires->js_call_amd('enrol_stripepaymentpro/stripe_payment_pro', 'initCouponSettings', []);

        echo $OUTPUT->footer();
    }

    /**
     * Handle receipt URL download management page
     *
     * @return void
     */
    public static function handle_receipt_page() {
        global $CFG, $PAGE, $OUTPUT, $USER;

        require_login();

        // Check if the user is an admin
        if (!is_siteadmin($USER->id)) {
            redirect($CFG->wwwroot);
        }

        // Set up page
        $PAGE->set_context(\context_system::instance());
        $PAGE->set_title(get_string('receipt_management', 'enrol_stripepaymentpro'));
        $PAGE->set_heading(get_string('receipt_management', 'enrol_stripepaymentpro'));
        $PAGE->set_url($CFG->wwwroot . '/enrol/stripepaymentpro/receipt_management.php');
        $PAGE->set_pagelayout('admin');

        $receiptcontroller = new receiptcontroller();

        // Get receipt data
        $receipt_data = $receiptcontroller->getreceiptsdata();

        // Prepare template data
        $templatedata = [
            'receipts' => $receiptcontroller->preparereceipttemplatedata($receipt_data),
            'hasreceipts' => !empty($receipt_data)
        ];

        echo $OUTPUT->header();

        // Render receipt management template
        echo $OUTPUT->render_from_template('enrol_stripepaymentpro/receipt_management', $templatedata);

        echo $OUTPUT->footer();
    }

    /**
     * Handle payment success page
     *
     * @return void
     */
    public static function handlepaymentsuccess() {
        global $CFG;

        require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
        $plugin = enrol_get_plugin('stripepaymentpro');
        \Stripe\Stripe::setApiKey($plugin->getcurrentsecretkey());

        // Get the session ID from the URL parameter
        $sessionid = optional_param('session_id', '', PARAM_RAW);

        $webhookController = new enrol_stripepaymentpro_webhook_controller();
        $webhookController->handlepaymentsuccess($sessionid);
    }
}
